/**
 * Currency formatting utilities based on country configurations
 * Now using @flinkk/money-math for standardized currency handling
 */

import {
  formatCurrency as formatCurrencyMath,
  toMinorUnits,
} from "@flinkk/money-math";

export interface CurrencyConfig {
  currencyCode: string;
  currencySymbol: string;
  decimalPlaces: number;
  decimalSeparator: "DOT" | "COMMA";
  thousandsSeparator: "COMMA" | "DOT" | "SPACE" | "APOSTROPHE" | "NONE";
  currencyPosition:
    | "BEFORE"
    | "AFTER"
    | "BEFORE_WITH_SPACE"
    | "AFTER_WITH_SPACE";
}

export interface TaxConfig {
  primaryTaxType: string;
  primaryTaxRate: number;
  primaryTaxName: string;
  secondaryTaxType?: string;
  secondaryTaxRate?: number;
  secondaryTaxName?: string;
  taxInclusivePricing: boolean;
  compoundTax: boolean;
  hasRegionalTax: boolean;
  regionalTaxRates?: any;
}

export interface CountryConfig {
  countryCode: string;
  countryName: string;
  currency: CurrencyConfig;
  tax: TaxConfig;
}

/**
 * Format currency amount using @flinkk/money-math
 * Maintains backward compatibility with existing CurrencyConfig interface
 */
export function formatCurrency(
  amount: number,
  config: CurrencyConfig,
  options: {
    showSymbol?: boolean;
    showCode?: boolean;
    useSymbolOnly?: boolean;
  } = {},
): string {
  const { showSymbol = true, showCode = false } = options;

  // Convert amount to minor units and format using money-math
  const minorUnits = toMinorUnits(amount, config.currencyCode);

  // Use money-math for standardized formatting
  if (showCode) {
    return formatCurrencyMath(minorUnits, config.currencyCode, {
      style: "code",
    });
  } else if (!showSymbol) {
    return formatCurrencyMath(minorUnits, config.currencyCode, {
      style: "number",
    });
  } else {
    return formatCurrencyMath(minorUnits, config.currencyCode);
  }
}

/**
 * Calculate tax amount based on country-specific tax configuration
 */
export function calculateTax(
  amount: number,
  taxConfig: TaxConfig,
  options: {
    includePrimary?: boolean;
    includeSecondary?: boolean;
    isAmountTaxInclusive?: boolean;
  } = {},
): {
  primaryTax: number;
  secondaryTax: number;
  totalTax: number;
  amountExcludingTax: number;
  amountIncludingTax: number;
} {
  const {
    includePrimary = true,
    includeSecondary = true,
    isAmountTaxInclusive = taxConfig.taxInclusivePricing,
  } = options;

  let baseAmount = amount;
  let primaryTax = 0;
  let secondaryTax = 0;

  if (isAmountTaxInclusive) {
    // Calculate tax from tax-inclusive amount
    if (includePrimary && taxConfig.primaryTaxRate > 0) {
      primaryTax =
        (amount * taxConfig.primaryTaxRate) / (100 + taxConfig.primaryTaxRate);
      baseAmount = amount - primaryTax;
    }

    if (
      includeSecondary &&
      taxConfig.secondaryTaxRate &&
      taxConfig.secondaryTaxRate > 0
    ) {
      if (taxConfig.compoundTax) {
        // Secondary tax calculated on amount including primary tax
        secondaryTax =
          (amount * taxConfig.secondaryTaxRate) /
          (100 + taxConfig.secondaryTaxRate);
      } else {
        // Secondary tax calculated on base amount
        secondaryTax = (baseAmount * taxConfig.secondaryTaxRate) / 100;
      }
    }
  } else {
    // Calculate tax from tax-exclusive amount
    if (includePrimary && taxConfig.primaryTaxRate > 0) {
      primaryTax = (amount * taxConfig.primaryTaxRate) / 100;
    }

    if (
      includeSecondary &&
      taxConfig.secondaryTaxRate &&
      taxConfig.secondaryTaxRate > 0
    ) {
      if (taxConfig.compoundTax) {
        // Secondary tax calculated on amount including primary tax
        secondaryTax =
          ((amount + primaryTax) * taxConfig.secondaryTaxRate) / 100;
      } else {
        // Secondary tax calculated on base amount
        secondaryTax = (amount * taxConfig.secondaryTaxRate) / 100;
      }
    }
  }

  const totalTax = primaryTax + secondaryTax;
  const amountExcludingTax = isAmountTaxInclusive ? amount - totalTax : amount;
  const amountIncludingTax = isAmountTaxInclusive ? amount : amount + totalTax;

  return {
    primaryTax: Number(primaryTax.toFixed(2)),
    secondaryTax: Number(secondaryTax.toFixed(2)),
    totalTax: Number(totalTax.toFixed(2)),
    amountExcludingTax: Number(amountExcludingTax.toFixed(2)),
    amountIncludingTax: Number(amountIncludingTax.toFixed(2)),
  };
}

/**
 * Get the appropriate thousands separator character
 */
function getThousandsSeparator(
  separator: CurrencyConfig["thousandsSeparator"],
): string {
  switch (separator) {
    case "COMMA":
      return ",";
    case "DOT":
      return ".";
    case "SPACE":
      return " ";
    case "APOSTROPHE":
      return "'";
    case "NONE":
    default:
      return "";
  }
}

/**
 * Get the appropriate decimal separator character
 */
function getDecimalSeparator(
  separator: CurrencyConfig["decimalSeparator"],
): string {
  switch (separator) {
    case "COMMA":
      return ",";
    case "DOT":
    default:
      return ".";
  }
}

/**
 * Parse a formatted currency string back to a number
 */
export function parseCurrency(
  formattedAmount: string,
  config: CurrencyConfig,
): number {
  // Remove currency symbol and code
  let cleanAmount = formattedAmount
    .replace(config.currencySymbol, "")
    .replace(config.currencyCode, "")
    .trim();

  // Remove thousands separators
  const thousandsSep = getThousandsSeparator(config.thousandsSeparator);
  if (thousandsSep) {
    cleanAmount = cleanAmount.replace(new RegExp(`\\${thousandsSep}`, "g"), "");
  }

  // Convert decimal separator to dot
  const decimalSep = getDecimalSeparator(config.decimalSeparator);
  if (decimalSep !== ".") {
    cleanAmount = cleanAmount.replace(decimalSep, ".");
  }

  return parseFloat(cleanAmount) || 0;
}
