import NoAccessSvg from "@flinkk/illustrations/no-access";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import Link from "next/link";
import { Card, CardContent } from "@flinkk/components/ui/card";
import { Typography } from "@flinkk/components/ui/typography";

export const NoAccessCard = () => {
  return (
    <Card className="flex h-[88vh] shrink-0 items-center justify-center rounded-md border border-dashed p-4 shadow-none">
      <CardContent className="flex flex-col items-center justify-center p-12 text-center space-y-2">
        <NoAccessSvg className="w-full max-w-sm" />
        <Typography type="h2" className="mt-4 text-destructive">
          Access Restricted
        </Typography>
        <Typography type="p">
          You don’t have access to this section. Contact your admin to request
          the necessary permissions.
        </Typography>
        <Button asChild className="btn-animate mt-4">
          <Link href="/dashboard">Go to Dashboard</Link>
        </Button>
      </CardContent>
    </Card>
  );
};
