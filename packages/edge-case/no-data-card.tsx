import { Card, CardContent } from "@flinkk/components/ui/card";
import { But<PERSON> } from "@flinkk/components/ui/button";
import Link from "next/link";
import NoDataSvg from "../illustrations/no-data";
import { Typography } from "@flinkk/components/ui/typography";

export const NoDataCard = ({ title, description, href, ctaName }: any) => {
  return (
    <Card className="flex h-[88vh] shrink-0 items-center justify-center rounded-md border border-dashed p-4 shadow-none">
      <CardContent className="flex flex-col items-center justify-center p-12 text-center space-y-2">
        <NoDataSvg />
        <Typography type="h2" className="mt-4">
          {title}
        </Typography>
        <Typography type="p" className="max-w-[520px]">{description}</Typography>
        {href && ctaName && (
          <Button className="btn-animate mt-4" asChild>
            <Link href={href}>{ctaName}</Link>
          </Button>
        )}
      </CardContent>
    </Card>
  );
};
