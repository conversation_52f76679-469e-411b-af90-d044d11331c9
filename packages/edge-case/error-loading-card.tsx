import ErrorLoadingSvg from "@flinkk/illustrations/error-loading";
import { Button } from "@flinkk/components/ui/button";
import {Card} from "@flinkk/components/ui/card";

interface ErrorLoadingCardProps {
  buttonClick: () => void;
}

export const ErrorLoadingCard = ({ buttonClick }: ErrorLoadingCardProps) => {
  return (
    <Card className="flex h-[88vh] shrink-0 items-center justify-center rounded-md border border-dashed p-4 shadow-none">
      <ErrorLoadingSvg className="w-full max-w-sm" />
      <h2 className="font-heading my-4 text-2xl font-bold text-destructive sm:text-3xl">
        Oops! Something Went Wrong
      </h2>
      <div className="mt-2 max-w-xl text-muted-foreground">
        <p>
          We encountered an unexpected issue while loading the opportunities.
          Our technical team has been notified.
        </p>
        <p className="mt-2">
          Please try again, or if the issue persists, contact support.
        </p>
      </div>
      <div className="mt-8 flex justify-center gap-4">
        <Button onClick={() => buttonClick()} variant="outline">
          Try Again
        </Button>
      </div>
    </Card>
  );
};
