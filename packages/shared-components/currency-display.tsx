import React from "react";
import { useCurrencyFormatterV2 } from "../shared-hooks/use-currency-formatter-v2";
import { cn } from "@flinkk/components/lib/utils";
import {
  formatCurrency as formatCurrencyMath,
  toMinorUnits,
} from "@flinkk/money-math";

export interface CurrencyDisplayProps {
  amount: number;
  className?: string;
  showSymbol?: boolean;
  showCode?: boolean;
  useSymbolOnly?: boolean;
  fallbackCurrency?: string;
  fallbackSymbol?: string;
}

/**
 * Component to display currency amounts with country-specific formatting
 */
export function CurrencyDisplay({
  amount,
  className,
  showSymbol = true,
  showCode = false,
  useSymbolOnly = true,
  fallbackCurrency = "USD",
  fallbackSymbol = "$",
}: CurrencyDisplayProps) {
  const { formatCurrency, isLoading, error } = useCurrencyFormatterV2();

  // Show loading state
  if (isLoading) {
    return (
      <span className={cn("text-muted-foreground", className)}>Loading...</span>
    );
  }

  // Show error state with fallback formatting using money-math
  if (error) {
    const minorUnits = toMinorUnits(amount, fallbackCurrency);
    const fallbackFormatted = formatCurrencyMath(minorUnits, fallbackCurrency);

    return (
      <span
        className={cn("text-foreground", className)}
        title="Using fallback currency formatting"
      >
        {fallbackFormatted}
      </span>
    );
  }

  // Format using country-specific configuration
  const formattedAmount = formatCurrency(amount, {
    showSymbol,
    showCode,
    useSymbolOnly,
  });

  return (
    <span className={cn("text-foreground", className)}>{formattedAmount}</span>
  );
}

/**
 * Component for editable currency input with country-specific formatting
 */
export interface CurrencyInputProps {
  value: number;
  onChange: (value: number) => void;
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  min?: number;
  max?: number;
  step?: number;
}

export function CurrencyInput({
  value,
  onChange,
  placeholder = "0.00",
  className,
  disabled = false,
  min,
  max,
  step,
}: CurrencyInputProps) {
  const { formatCurrency, parseCurrency, currencyConfig, isLoading } =
    useCurrencyFormatterV2();
  const [displayValue, setDisplayValue] = React.useState("");
  const [isFocused, setIsFocused] = React.useState(false);

  // Update display value when value prop changes
  React.useEffect(() => {
    if (!isFocused && !isLoading) {
      setDisplayValue(formatCurrency(value));
    }
  }, [value, formatCurrency, isFocused, isLoading]);

  const handleFocus = () => {
    setIsFocused(true);
    // Show raw number when focused for easier editing
    setDisplayValue(value.toString());
  };

  const handleBlur = () => {
    setIsFocused(false);
    // Parse and format the value
    const parsedValue = parseFloat(displayValue) || 0;
    onChange(parsedValue);
    setDisplayValue(formatCurrency(parsedValue));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    setDisplayValue(inputValue);

    // If not focused, parse and update immediately
    if (!isFocused) {
      const parsedValue = parseCurrency(inputValue);
      onChange(parsedValue);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Allow Enter to blur and format
    if (e.key === "Enter") {
      e.currentTarget.blur();
    }
  };

  if (isLoading) {
    return (
      <input
        type="text"
        value="Loading..."
        disabled
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className,
        )}
      />
    );
  }

  return (
    <div className="relative">
      <input
        type="text"
        value={displayValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        disabled={disabled}
        min={min}
        max={max}
        step={
          step ||
          (currencyConfig.decimalPlaces > 0
            ? `0.${"0".repeat(currencyConfig.decimalPlaces - 1)}1`
            : "1")
        }
        className={cn(
          "flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",
          className,
        )}
      />
      {!isFocused && currencyConfig.currencyPosition.includes("BEFORE") && (
        <span className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground pointer-events-none">
          {currencyConfig.currencySymbol}
        </span>
      )}
      {!isFocused && currencyConfig.currencyPosition.includes("AFTER") && (
        <span className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground pointer-events-none">
          {currencyConfig.currencySymbol}
        </span>
      )}
    </div>
  );
}

/**
 * Component to display tax information with country-specific formatting
 */
export interface TaxDisplayProps {
  amount: number;
  taxRate?: number;
  className?: string;
  showBreakdown?: boolean;
}

export function TaxDisplay({
  amount,
  taxRate,
  className,
  showBreakdown = false,
}: TaxDisplayProps) {
  const { formatCurrency } = useCurrencyFormatterV2();

  // For tax calculations, use the calculateTaxBreakdown function from packages/shared-utils/tax-calculator.ts
  // This component needs to be updated to use the proper tax calculation utility
  // For now, showing a simplified tax display

  if (!taxRate) {
    return (
      <span className={cn("text-muted-foreground", className)}>
        No tax rate provided
      </span>
    );
  }

  const taxAmount = (amount * taxRate) / 100;
  const totalAmount = amount + taxAmount;

  if (!showBreakdown) {
    return (
      <span className={cn("text-foreground", className)}>
        {formatCurrency(taxAmount)}
      </span>
    );
  }

  return (
    <div className={cn("space-y-1", className)}>
      <div className="flex justify-between text-sm">
        <span>Tax ({taxRate}%):</span>
        <span>{formatCurrency(taxAmount)}</span>
      </div>
      <div className="flex justify-between font-medium border-t pt-1">
        <span>Total:</span>
        <span>{formatCurrency(totalAmount)}</span>
      </div>
    </div>
  );
}

// Tax calculation functionality has been moved to packages/shared-utils/tax-calculator.ts
