import { type Table as TanstackTable, flexRender } from "@tanstack/react-table";
import type * as React from "react";

import { DataTablePagination } from "./data-table-pagination";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@flinkk/components/ui/table";
import { getCommonPinningStyles } from "../lib/data-table";
import { cn } from "@flinkk/lib/cn";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";

interface DataTableProps<TData> extends React.ComponentProps<"div"> {
  table: TanstackTable<TData>;
  actionBar?: React.ReactNode;
  onRowClick?: (row: TData) => void;
  doctype: string;
  hidePagination?: boolean;
}

export function DataTable<TData>({
  table,
  actionBar,
  children,
  className,
  onRowClick,
  doctype,
  hidePagination = false,
  ...props
}: DataTableProps<TData>) {
  return (
    <div
      className={cn(
        "flex w-full flex-col gap-2.5 overflow-auto space-y-2",
        className
      )}
      {...props}
    >
      {children}
      <div className="rounded-md border mt-1 mb-1">
        <Table data-testid={`${doctype.toLowerCase()}-list`}>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead
                    key={header.id}
                    colSpan={header.colSpan}
                    style={{
                      ...getCommonPinningStyles({ column: header.column }),
                    }}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  data-testid={`${doctype.toLowerCase()}-item`}
                  className={cn(
                    onRowClick && "cursor-pointer hover:bg-muted/50"
                  )}
                  tabIndex={onRowClick ? 0 : undefined}
                  role={onRowClick ? "button" : undefined}
                  aria-label={
                    onRowClick ? `View ${doctype} details` : undefined
                  }
                  onKeyDown={(e) => {
                    if (onRowClick && (e.key === "Enter" || e.key === " ")) {
                      e.preventDefault();
                      onRowClick(row.original);
                    }
                  }}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      style={{
                        ...getCommonPinningStyles({ column: cell.column }),
                      }}
                      onClick={() =>
                        cell.column.id === "select" ||
                        cell.column.id === "actions"
                          ? {}
                          : onRowClick?.(row.original)
                      }
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={table.getAllColumns().length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {!hidePagination && (
        <div className="flex flex-col gap-2.5">
          <DataTablePagination table={table} />
          {actionBar &&
            table.getFilteredSelectedRowModel().rows.length > 0 &&
            actionBar}
        </div>
      )}
      {hidePagination &&
        actionBar &&
        table.getFilteredSelectedRowModel().rows.length > 0 && (
          <div className="flex flex-col gap-2.5">{actionBar}</div>
        )}
    </div>
  );
}
