import { createSoftDeleteExtension as createSoftDelete } from "prisma-extension-soft-delete";

export const createSoftDeleteExtension = () => {
  return createSoftDelete({
    models: {
      Lead: true,
      Contact: true,
      Opportunity: true,
      Task: true,
      BusinessAccount: true,
      SupportTicket: true,
      Campaign: true,
      Product: true,
      Feedback: true,
      Note: true,
      Quote: true,
      Family: true,
      FamilyMember: true,
    },
    defaultConfig: {
      field: "deleted",
      createValue: (deleted) => {
        return deleted ? true : false;
      },
    },
  });
};
