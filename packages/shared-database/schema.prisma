generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL")
}

// ==========================================
// Base Models
// ==========================================

model Session {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  sessionToken String   @unique
  userId       String   @db.ObjectId
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model User {
  id                             String                   @id @default(auto()) @map("_id") @db.ObjectId
  name                           String?
  email                          String?                  @unique
  emailVerified                  DateTime?
  password                       String?
  image                          String?
  role                           UserRole                 @default(USER)
  lastLogin                      DateTime?
  notificationPreferences        String? // JSON string for notification preferences
  createdAt                      DateTime                 @default(now())
  updatedAt                      DateTime                 @updatedAt
  authAccounts                   AuthAccount[]            @relation("AuthAccountUser")
  sessions                       Session[]
  membership                     MemberShip[]
  leads                          Lead[]
  opportunities                  Opportunity[]
  contacts                       Contact[]
  tasks                          Task[]
  events                         Event[]
  businessAccounts               BusinessAccount[]        @relation("UserBusinessAccounts")
  activityLogs                   ActivityLog[]
  campaigns                      Campaign[]
  createdTenants                 Tenant[]                 @relation("CreatedBy")
  updatedTenants                 Tenant[]                 @relation("UpdatedBy")
  createdLeads                   Lead[]                   @relation("LeadCreatedBy")
  updatedLeads                   Lead[]                   @relation("LeadUpdatedBy")
  createdOpportunities           Opportunity[]            @relation("OpportunityCreatedBy")
  updatedOpportunities           Opportunity[]            @relation("OpportunityUpdatedBy")
  createdContacts                Contact[]                @relation("ContactCreatedBy")
  updatedContacts                Contact[]                @relation("ContactUpdatedBy")
  createdTasks                   Task[]                   @relation("TaskCreatedBy")
  updatedTasks                   Task[]                   @relation("TaskUpdatedBy")
  createdEvents                  Event[]                  @relation("EventCreatedBy")
  updatedEvents                  Event[]                  @relation("EventUpdatedBy")
  createdBusinessAccounts        BusinessAccount[]        @relation("BusinessAccountCreatedBy")
  updatedBusinessAccounts        BusinessAccount[]        @relation("BusinessAccountUpdatedBy")
  createdPipelineStages          PipelineStage[]          @relation("PipelineStageCreatedBy")
  updatedPipelineStages          PipelineStage[]          @relation("PipelineStageUpdatedBy")
  createdProducts                Product[]                @relation("ProductCreatedBy")
  updatedProducts                Product[]                @relation("ProductUpdatedBy")
  createdOpportunityProducts     OpportunityProduct[]     @relation("OpportunityProductCreatedBy")
  updatedOpportunityProducts     OpportunityProduct[]     @relation("OpportunityProductUpdatedBy")
  createdOpportunityPackages     OpportunityPackage[]     @relation("OpportunityPackageCreatedBy")
  updatedOpportunityPackages     OpportunityPackage[]     @relation("OpportunityPackageUpdatedBy")
  createdSupportTickets          SupportTicket[]          @relation("SupportTicketCreatedBy")
  lastUpdatedSupportTickets      SupportTicket[]          @relation("SupportTicketUpdatedBy")
  assignedSupportTickets         SupportTicket[]          @relation("TicketAssignee")
  createdCampaigns               Campaign[]               @relation("CampaignCreatedBy")
  updatedCampaigns               Campaign[]               @relation("CampaignUpdatedBy")
  createdStatusOptions           StatusOption[]           @relation("StatusOptionCreatedBy")
  updatedStatusOptions           StatusOption[]           @relation("StatusOptionUpdatedBy")
  createdPipelineStageConfigs    PipelineStageConfig[]    @relation("PipelineStageConfigCreatedBy")
  updatedPipelineStageConfigs    PipelineStageConfig[]    @relation("PipelineStageConfigUpdatedBy")
  dashboardLayouts               DashboardLayout[]
  createdDashboardLayouts        DashboardLayout[]        @relation("DashboardLayoutCreatedBy")
  updatedDashboardLayouts        DashboardLayout[]        @relation("DashboardLayoutUpdatedBy")
  createdDashboardWidgets        DashboardWidget[]        @relation("DashboardWidgetCreatedBy")
  updatedDashboardWidgets        DashboardWidget[]        @relation("DashboardWidgetUpdatedBy")
  createdCustomFields            CustomField[]            @relation("CustomFieldCreatedBy")
  updatedCustomFields            CustomField[]            @relation("CustomFieldUpdatedBy")
  createdCustomFieldValues       CustomFieldValue[]       @relation("CustomFieldValueCreatedBy")
  updatedCustomFieldValues       CustomFieldValue[]       @relation("CustomFieldValueUpdatedBy")
  createdFieldPermissions        FieldPermission[]        @relation("FieldPermissionCreatedBy")
  updatedFieldPermissions        FieldPermission[]        @relation("FieldPermissionUpdatedBy")
  createdDefaultFieldPermissions DefaultFieldPermission[] @relation("DefaultFieldPermissionCreatedBy")
  updatedDefaultFieldPermissions DefaultFieldPermission[] @relation("DefaultFieldPermissionUpdatedBy")
  createdInboxes                 Inbox[]                  @relation("InboxCreatedBy")
  updatedInboxes                 Inbox[]                  @relation("InboxUpdatedBy")
  createdConversations           Conversation[]           @relation("ConversationCreatedBy")
  updatedConversations           Conversation[]           @relation("ConversationUpdatedBy")
  assignedConversations          Conversation[]           @relation("AssignedConversations")
  sentMessages                   Message[]                @relation("SentMessages")
  participations                 Participant[]
  createdLabels                  Label[]                  @relation("LabelCreatedBy")
  updatedLabels                  Label[]                  @relation("LabelUpdatedBy")
  inboxAgents                    InboxAgent[]
  createdFeedback                Feedback[]               @relation("FeedbackCreatedBy")
  updatedFeedback                Feedback[]               @relation("FeedbackUpdatedBy")
  feedbackVotes                  FeedbackVote[]           @relation("UserFeedbackVotes")
  feedbackComments               FeedbackComment[]        @relation("UserFeedbackComments")
  notes                          Note[]
  uploadedTicketAttachments      TicketAttachment[]       @relation("TicketAttachmentUploader")
  SupportTicket                  SupportTicket[]
  TicketAttachment               TicketAttachment[]

  // Quote Relations
  ownedQuotes   Quote[] @relation("QuoteOwner")
  createdQuotes Quote[] @relation("QuoteCreatedBy")
  updatedQuotes Quote[] @relation("QuoteUpdatedBy")

  // Approval Workflow Relations
  requestedApprovals   TicketApproval[] @relation("ApprovalRequester")
  reviewedApprovals    TicketApproval[] @relation("ApprovalReviewer")
  assignedRoadmapItems RoadmapItem[]    @relation("RoadmapAssignee")
  assignedTaskItems    TaskItem[]       @relation("TaskItemAssignee")

  // Package Relations
  createdPackages Package[] @relation("PackageCreatedBy")
  updatedPackages Package[] @relation("PackageUpdatedBy")

  // Price Book Relations
  createdPriceBooks       PriceBook[]      @relation("PriceBookCreatedBy")
  updatedPriceBooks       PriceBook[]      @relation("PriceBookUpdatedBy")
  createdPriceBookEntries PriceBookEntry[] @relation("PriceBookEntryCreatedBy")
  updatedPriceBookEntries PriceBookEntry[] @relation("PriceBookEntryUpdatedBy")

  // Custom Role Relations
  createdCustomRoles CustomRole[] @relation("CustomRoleCreatedBy")
  updatedCustomRoles CustomRole[] @relation("CustomRoleUpdatedBy")

  // Country Configuration Relations
  createdCountryConfigs CountryConfiguration[] @relation("CountryConfigCreatedBy")
  updatedCountryConfigs CountryConfiguration[] @relation("CountryConfigUpdatedBy")
  createdTaxCategories  TaxCategory[]          @relation("TaxCategoryCreatedBy")
  updatedTaxCategories  TaxCategory[]          @relation("TaxCategoryUpdatedBy")
  
  // Currency Configuration Relations
  createdCurrencyConfigs CurrencyConfiguration[] @relation("CurrencyConfigCreatedBy")
  updatedCurrencyConfigs CurrencyConfiguration[] @relation("CurrencyConfigUpdatedBy")

  // Help Portal Relations
  createdHelpCategories HelpCategory[] @relation("HelpCategoryCreatedBy")
  updatedHelpCategories HelpCategory[] @relation("HelpCategoryUpdatedBy")
  authoredHelpArticles  HelpArticle[]  @relation("HelpArticleAuthor")
  updatedHelpArticles   HelpArticle[]  @relation("HelpArticleUpdatedBy")

  uploadedHelpAttachments   HelpAttachment[]     @relation("HelpAttachmentUploader")
  helpVotes                 HelpVote[]           @relation("HelpVoteUser")
  updatedHelpPortalSettings HelpPortalSettings[] @relation("HelpPortalSettingsUpdatedBy")

  // CRM Settings Relations
  createdLeadConversionMappings LeadConversionMapping[] @relation("LeadConversionMappingCreatedBy")
  updatedLeadConversionMappings LeadConversionMapping[] @relation("LeadConversionMappingUpdatedBy")
  // API and Webhook Relations
  createdApiKeys                ApiKey[]                @relation("ApiKeyCreatedBy")
  updatedApiKeys                ApiKey[]                @relation("ApiKeyUpdatedBy")
  createdWebhooks               WebhookConfig[]         @relation("WebhookConfigCreatedBy")
  updatedWebhooks               WebhookConfig[]         @relation("WebhookConfigUpdatedBy")

  // AI Chatbot Relations
  createdAgents          Agent[]          @relation("AgentCreatedBy")
  updatedAgents          Agent[]          @relation("AgentUpdatedBy")
  threads                Thread[]         @relation("UserThreads")
  interactions           Interaction[]    @relation("UserInteractions")
  crmEmailConfigsCreated CRMEmailConfig[] @relation("CRMEmailConfigCreatedBy")
  crmEmailConfigsUpdated CRMEmailConfig[] @relation("CRMEmailConfigUpdatedBy")
  emailMessages          EmailMessage[]   @relation("EmailMessageUser")
}

model VerificationToken {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
}

model AuthAccount {
  id                String  @id @default(auto()) @map("_id") @db.ObjectId
  userId            String  @db.ObjectId
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  user              User    @relation("AuthAccountUser", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("Account") // Maps to the existing Account table in the database
}

// Models for configurable business logic
model StatusOption {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  type        String
  value       String
  label       String
  description String?
  color       String?
  icon        String?
  order       Int      @default(0)
  isDefault   Boolean  @default(false)
  isSystem    Boolean  @default(false)
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?  @relation("StatusOptionCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?  @relation("StatusOptionUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([tenantId, type, value])
  @@index([type])
}

model PipelineStageConfig {
  id          String           @id @default(auto()) @map("_id") @db.ObjectId
  stage       OpportunityStage
  label       String
  description String?
  color       String?
  icon        String?
  order       Int              @default(0)
  tenantId    String           @db.ObjectId
  createdById String?          @db.ObjectId
  updatedById String?          @db.ObjectId
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?  @relation("PipelineStageConfigCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?  @relation("PipelineStageConfigUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([tenantId, stage])
}

// Models for dashboard configuration
model DashboardLayout {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  isDefault   Boolean  @default(false)
  isSystem    Boolean  @default(false)
  userId      String?  @db.ObjectId
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user      User?             @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant    Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?             @relation("DashboardLayoutCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?             @relation("DashboardLayoutUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  widgets   DashboardWidget[]

  @@unique([tenantId, userId, name])
}

model DashboardWidget {
  id                String   @id @default(auto()) @map("_id") @db.ObjectId
  type              String
  title             String
  description       String?
  icon              String?
  size              String
  dataSource        String
  config            Json?
  order             Int      @default(0)
  dashboardLayoutId String   @db.ObjectId
  createdById       String?  @db.ObjectId
  updatedById       String?  @db.ObjectId
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  dashboardLayout DashboardLayout @relation(fields: [dashboardLayoutId], references: [id], onDelete: Cascade)
  createdBy       User?           @relation("DashboardWidgetCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy       User?           @relation("DashboardWidgetUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
}

model Tenant {
  id       String  @id @default(auto()) @map("_id") @db.ObjectId
  name     String
  domain   String? // Legacy domain field (keep for backward compatibility)
  logo     String? // S3 URL for organization logo
  slug     String?
  timezone String?

  // Organization Details for Quotations
  businessName String?
  gstin        String?
  address      String?
  city         String?
  postalCode   String?
  state        String?
  country      String?
  
  // Currency and Tax Settings
  currency     Currency? // Primary currency for the organization
  taxSystem    String?   // Tax system (e.g., "VAT (20%)", "GST (18%)")
  taxInclusive String?   // Whether prices include tax ("Yes" or "No")

  // Quote Template Configuration
  quoteTemplateId String? @default("professional") // Default template ID

  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  createdBy               User?                    @relation("CreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy               User?                    @relation("UpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  users                   MemberShip[]
  contacts                Contact[]
  events                  Event[]
  leads                   Lead[]
  opportunities           Opportunity[]
  tasks                   Task[]
  businessAccounts        BusinessAccount[]        @relation("OrganizationBusinessAccounts")
  pipelineStages          PipelineStage[]
  products                Product[]
  productCategories       ProductCategory[]
  units                   Unit[]
  activityLogs            ActivityLog[]
  supportTickets          SupportTicket[]
  campaigns               Campaign[]
  statusOptions           StatusOption[]
  pipelineStageConfigs    PipelineStageConfig[]
  dashboardLayouts        DashboardLayout[]
  customFields            CustomField[]
  fieldPermissions        FieldPermission[]
  defaultFieldPermissions DefaultFieldPermission[]
  inboxes                 Inbox[]
  conversations           Conversation[]
  messages                Message[]
  labels                  Label[]
  feedback                Feedback[]
  notes                   Note[]
  TicketAttachment        TicketAttachment[]

  // Quote Relations
  quotes              Quote[]
  quoteLines          QuoteLine[]
  opportunityProducts OpportunityProduct[]
  opportunityPackages OpportunityPackage[] // Packages associated with opportunities
  TicketApproval      TicketApproval[]
  RoadmapItem         RoadmapItem[]
  EmailDraft          EmailDraft[]
  TaskItem            TaskItem[]
  EventItem           EventItem[]

  // Package Relations
  packages     Package[]
  packageLines PackageLine[]

  // Price Book Relations
  priceBooks       PriceBook[]
  priceBookEntries PriceBookEntry[]

  // Country Configuration Relations
  countryConfigurations CountryConfiguration[]
  taxCategories         TaxCategory[]
  
  // Currency Configuration Relations
  currencyConfigurations CurrencyConfiguration[]

  // Custom Role Relations
  customRoles CustomRole[]

  // Help Portal Relations
  helpCategories     HelpCategory[]
  helpArticles       HelpArticle[]
  helpAttachments    HelpAttachment[]
  helpVotes          HelpVote[]
  helpPortalSettings HelpPortalSettings?

  // Email Support Relations
  emailProcessingLogs   EmailProcessingLog[]
  HelpPortalConfig      HelpPortalConfig?
  EmailTemplate         EmailTemplate[]
  ApiKey                ApiKey[]
  WebhookConfig         WebhookConfig[]
  WebhookDelivery       WebhookDelivery[]
  LeadConversionMapping LeadConversionMapping[]
  Domain                Domain[]

  // AI Chatbot Relations
  agents       Agent[]
  threads      Thread[]
  interactions Interaction[]

  // Mail Configuration Relations

  // Inventory Configuration Relations
  inventoryConfiguration InventoryConfiguration?
  mailConfiguration      MailConfiguration?
  WhatsAppMessage        WhatsAppMessage[]
  crmEmailConfigs        CRMEmailConfig[]
  EmailMessage           EmailMessage[]
  families               Family[]
  familyMembers          FamilyMember[]
}

model MemberShip {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  userId    String   @db.ObjectId
  tenantId  String   @db.ObjectId
  roleId    String?  @db.ObjectId // Reference to custom role
  isDefault Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  tenant     Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  customRole CustomRole? @relation(fields: [roleId], references: [id], onDelete: SetNull)

  @@unique([userId, tenantId])
}

// Custom Roles System
model CustomRole {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  tenantId    String   @db.ObjectId
  isSystem    Boolean  @default(false) // System roles (Admin, Owner) cannot be deleted
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant      Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy   User?            @relation("CustomRoleCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy   User?            @relation("CustomRoleUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  memberships MemberShip[]
  permissions RolePermission[]

  @@unique([name, tenantId]) // Role names must be unique within a tenant
}

model RolePermission {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  roleId    String   @db.ObjectId
  app       String // CRM, Support, AdminCenter, Mail
  module    String // Leads, Contacts, Opportunities, etc.
  canView   Boolean  @default(false)
  canCreate Boolean  @default(false)
  canEdit   Boolean  @default(false)
  canDelete Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  role CustomRole @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([roleId, app, module]) // One permission record per role-app-module combination
}

model Lead {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  // Personal Information
  salutation  String?
  firstName   String?
  lastName    String?
  title       String? // Job Title/Position
  company     String?
  website     String?
  description String?

  // Contact Information
  email           String?
  phone           String?
  additionalPhone String?
  country         String?
  streetAddress   String?
  city            String?
  postalCode      String?
  state           String? // State/Province

  // Business Information
  numberOfEmployees Int?
  companySize       String? // Company size range (e.g., "0-10 employees", "11-20 employees", "50+ employees")
  annualRevenue     String?
  industry          String?
  source            String?

  // System fields
  status             String       @default("OPEN") // Lead Status: OPEN, CONVERTED, LOST
  stage              String       @default("NEW") // Lead Stage: NEW, CONTACTED, QUALIFIED, NURTURING, UNQUALIFIED
  notes              String?
  requirements       String? // Customer requirements and specifications
  leadScore          Int?
  tags               String? // JSON array stored as string
  priority           LeadPriority @default(MEDIUM)
  isB2B              Boolean      @default(false)
  isConverted        Boolean      @default(false)
  deleted            Boolean      @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt          DateTime? // Soft delete field - null when active, DateTime when deleted
  convertedAccountId String?      @db.ObjectId
  convertedContactId String?      @db.ObjectId
  userId             String?      @db.ObjectId
  tenantId           String       @db.ObjectId
  campaignId         String?      @db.ObjectId
  createdById        String?      @db.ObjectId
  updatedById        String?      @db.ObjectId
  createdAt          DateTime     @default(now())
  updatedAt          DateTime     @updatedAt

  // Relations
  user                   User?              @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant                 Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  campaign               Campaign?          @relation("CampaignLeads", fields: [campaignId], references: [id], onDelete: SetNull)
  convertedAccount       BusinessAccount?   @relation("LeadConvertedAccount", fields: [convertedAccountId], references: [id], onDelete: SetNull)
  convertedContact       Contact?           @relation("LeadConvertedContact", fields: [convertedContactId], references: [id], onDelete: SetNull)
  createdBy              User?              @relation("LeadCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy              User?              @relation("LeadUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  convertedOpportunities Opportunity[]      @relation("LeadConvertedOpportunity")
  customFields           CustomFieldValue[] @relation("LeadCustomFields")
  contacts               Contact[]          @relation("ContactFromLead")
  EmailMessage           EmailMessage[]
}

model EmailTemplate {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  subject     String
  content     Json     @db.Json
  templateKey String   @unique // e.g., magic_link, password_reset, etc.
  status      String   @default("active") // active, draft, archived
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  tenantId    String   @db.ObjectId

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
}

model Opportunity {
  id                  String              @id @default(auto()) @map("_id") @db.ObjectId
  dealId              String? // Human-readable opportunity ID
  dealName            String // Human-readable title (mapped from name)
  description         String? // Long text description
  contactId           String?             @db.ObjectId // Primary person involved
  accountId           String?             @db.ObjectId // Organization or household
  relatedToType       RelatedToType       @default(LEAD) // Lead, Contact, Account
  relatedToId         String              @db.ObjectId // Foreign key
  associatedContacts  String? // JSON array of contact IDs
  dealOwner           String              @db.ObjectId // Sales rep (mapped from userId)
  stage               String              @default("DISCOVERY") // Sales stage
  status              String              @default("OPEN") // Open, Closed-Won, Closed-Lost
  priority            OpportunityPriority @default(MEDIUM) // Priority level
  type                OpportunityType? // Opportunity type (New Business, Existing Business, etc.)
  value               Float? // Estimated value (mapped from value)
  currency            Currency            @default(USD) // USD, CHF, etc.
  expectedCloseDate   DateTime? // Forecast (mapped from closeDate)
  actualCloseDate     DateTime? // Final date of close
  source              String? // Inbound, Referral, etc.
  campaignId          String?             @db.ObjectId // Ties to Campaign object
  tags                String? // JSON array of tags
  createdById         String?             @db.ObjectId // Who created it
  createdAt           DateTime            @default(now()) // Timestamp
  updatedAt           DateTime            @updatedAt // Auto-updated
  convertedFromLeadId String?             @db.ObjectId // If opportunity started from a lead
  convertedAt         DateTime? // When it was created from lead
  lastContactedAt     DateTime? // Most recent interaction
  stageEnteredAt      DateTime? // Timestamp of when current stage was set
  probability         Int?                @default(0) // Forecasting weight
  bookingId           String?             @db.ObjectId // Booking record
  fulfillmentId       String?             @db.ObjectId // Fulfillment record

  // Legacy fields for backward compatibility
  name            String // Mapped to dealName
  notes           String? // Additional notes
  deleted         Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt       DateTime? // Soft delete field - null when active, DateTime when deleted
  userId          String?   @db.ObjectId // Mapped to dealOwner
  tenantId        String    @db.ObjectId
  pipelineStageId String?   @db.ObjectId
  updatedById     String?   @db.ObjectId

  // Relations
  user              User?                @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant            Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  account           BusinessAccount?     @relation("BusinessAccountOpportunities", fields: [accountId], references: [id], onDelete: SetNull)
  contact           Contact?             @relation("ContactOpportunities", fields: [contactId], references: [id], onDelete: SetNull)
  pipelineStage     PipelineStage?       @relation(fields: [pipelineStageId], references: [id], onDelete: SetNull)
  createdBy         User?                @relation("OpportunityCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy         User?                @relation("OpportunityUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  campaign          Campaign?            @relation("CampaignOpportunities", fields: [campaignId], references: [id], onDelete: SetNull)
  convertedFromLead Lead?                @relation("LeadConvertedOpportunity", fields: [convertedFromLeadId], references: [id], onDelete: SetNull)
  products          OpportunityProduct[]
  packages          OpportunityPackage[] // Packages associated with this opportunity
  customFields      CustomFieldValue[]   @relation("OpportunityCustomFields")
  CustomFieldValue  CustomFieldValue[]
  quotes            Quote[]
  EmailMessage      EmailMessage[]
}

model Contact {
  id                  String    @id @default(auto()) @map("_id") @db.ObjectId
  firstName           String
  middleName          String?
  lastName            String
  email               String
  phoneNumber         String
  additionalPhone     String?
  role                String?
  tags                String? // JSON array stored as string
  source              String?
  description         String?
  deleted             Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt           DateTime? // Soft delete field - null when active, DateTime when deleted
  userId              String?   @db.ObjectId // contact_owner
  tenantId            String    @db.ObjectId
  accountId           String?   @db.ObjectId
  campaignId          String?   @db.ObjectId
  leadId              String?   @db.ObjectId
  inventoryCustomerId String? // Stores the ID of the synced Inventory Customer
  createdById         String?   @db.ObjectId
  updatedById         String?   @db.ObjectId
  createdAt           DateTime  @default(now())
  updatedAt           DateTime  @updatedAt

  // Relations
  user                  User?                @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant                Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  account               BusinessAccount?     @relation("BusinessAccountContacts", fields: [accountId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  campaign              Campaign?            @relation("CampaignContacts", fields: [campaignId], references: [id], onDelete: SetNull)
  lead                  Lead?                @relation("ContactFromLead", fields: [leadId], references: [id], onDelete: NoAction, onUpdate: NoAction)
  createdBy             User?                @relation("ContactCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy             User?                @relation("ContactUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  opportunities         Opportunity[]        @relation("ContactOpportunities")
  supportTickets        SupportTicket[]      @relation("ContactSupportTickets")
  convertedFromLead     Lead[]               @relation("LeadConvertedContact")
  conversations         Conversation[]       @relation("ContactConversations")
  customFields          CustomFieldValue[]   @relation("ContactCustomFields")
  CustomFieldValue      CustomFieldValue[]
  feedback              Feedback[]           @relation("ContactFeedback")
  emailMessages         EmailMessage[]
  Campaign              Campaign?            @relation(fields: [campaignId], references: [id])
  primaryAccountContact BusinessAccount[]    @relation("AccountPrimaryContact")
  quotes                Quote[]
  EmailProcessingLog    EmailProcessingLog[]
  WhatsAppMessage       WhatsAppMessage[]
  families              Family[]
}

model Family {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  name      String   // e.g., "Sharma Family"
  contactId String   @db.ObjectId
  tenantId  String   @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  contact     Contact        @relation(fields: [contactId], references: [id], onDelete: Cascade)
  tenant      Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  members     FamilyMember[]

  @@index([contactId])
  @@index([tenantId])
}

model FamilyMember {
  id           String               @id @default(auto()) @map("_id") @db.ObjectId
  name         String               // Full name of family member
  dob          DateTime             // Date of birth
  relationship FamilyRelationship   // Relationship to the contact
  notes        String?              // Optional remarks
  familyId     String               @db.ObjectId
  tenantId     String               @db.ObjectId
  createdAt    DateTime             @default(now())
  updatedAt    DateTime             @updatedAt

  // Relations
  family    Family @relation(fields: [familyId], references: [id], onDelete: Cascade)
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([familyId])
  @@index([tenantId])
}

model Task {
  id                String       @id @default(auto()) @map("_id") @db.ObjectId
  title             String
  description       String?
  status            TaskStatus   @default(PENDING)
  priority          TaskPriority @default(MEDIUM)
  startDate         DateTime?    @default(now())
  dueDate           DateTime?
  reference_modal   String // To store the model name (e.g., "Lead", "Deal")
  reference_modalId String // To store the referenced model's ID
  deleted           Boolean      @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt         DateTime? // Soft delete field - null when active, DateTime when deleted
  createdAt         DateTime     @default(now())
  updatedAt         DateTime     @updatedAt

  // Relations
  userId           String?            @db.ObjectId // Assigned to
  tenantId         String             @db.ObjectId
  createdById      String?            @db.ObjectId
  updatedById      String?            @db.ObjectId
  user             User?              @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant           Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy        User?              @relation("TaskCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy        User?              @relation("TaskUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  customFields     CustomFieldValue[] @relation("TaskCustomFields")
  CustomFieldValue CustomFieldValue[]

  @@index([tenantId])
  @@index([userId])
  @@index([reference_modalId])
}

model Event {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  startDate   DateTime
  endDate     DateTime
  location    String?
  userId      String?  @db.ObjectId
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user      User?  @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?  @relation("EventCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?  @relation("EventUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
}

model BusinessAccount {
  id               String      @id @default(auto()) @map("_id") @db.ObjectId
  name             String // account_name
  accountType      AccountType @default(B2B) // account_type (B2B, B2C, Family, Corporate)
  industry         String? // industry (for B2B)
  region           String? // geographic area
  website          String?
  phone            String?
  email            String?
  emailDomain      String? // email_domain (e.g., "@powderbyrne.com")
  employees        Int?
  annualRevenue    String?
  billingAddress   String?
  shippingAddress  String?
  description      String? // context/background
  tags             String? // JSON array stored as string (VIP Client, Strategic Partner, etc.)
  primaryContactId String?     @db.ObjectId // primary_contact_id
  type             AccountType @default(CUSTOMER) // keeping for backward compatibility
  deleted          Boolean     @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt        DateTime? // Soft delete field - null when active, DateTime when deleted
  userId           String?     @db.ObjectId // account_owner
  tenantId         String      @db.ObjectId
  createdById      String?     @db.ObjectId
  updatedById      String?     @db.ObjectId
  createdAt        DateTime    @default(now()) // auto-generated
  updatedAt        DateTime    @updatedAt // auto-updated

  // Relations
  user              User?              @relation("UserBusinessAccounts", fields: [userId], references: [id], onDelete: SetNull)
  tenant            Tenant             @relation("OrganizationBusinessAccounts", fields: [tenantId], references: [id], onDelete: Cascade)
  primaryContact    Contact?           @relation("AccountPrimaryContact", fields: [primaryContactId], references: [id], onDelete: SetNull, onUpdate: NoAction)
  createdBy         User?              @relation("BusinessAccountCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy         User?              @relation("BusinessAccountUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  contacts          Contact[]          @relation("BusinessAccountContacts")
  opportunities     Opportunity[]      @relation("BusinessAccountOpportunities")
  supportTickets    SupportTicket[]    @relation("BusinessAccountSupportTickets")
  convertedFromLead Lead[]             @relation("LeadConvertedAccount")
  conversations     Conversation[]     @relation("AccountConversations")
  customFields      CustomFieldValue[] @relation("AccountCustomFields")
  feedback          Feedback[]         @relation("BusinessAccountFeedback")
  quotes            Quote[]
}

model PipelineStage {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  order       Int
  description String?
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant           Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy        User?              @relation("PipelineStageCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy        User?              @relation("PipelineStageUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  opportunities    Opportunity[]
  customFields     CustomFieldValue[] @relation("PipelineStageCustomFields")
  CustomFieldValue CustomFieldValue[]
}

model Product {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  sku         String?

  // Product type and classification
  type          ProductType   @default(GOODS)
  categoryId    String?       @db.ObjectId // Link to product category
  unitId        String?       @db.ObjectId
  hsnCode       String? // HSN/SAC code
  taxPreference TaxPreference @default(TAXABLE)

  // Pricing fields (renamed for clarity)
  sellingPrice Float // Renamed from 'price'
  costPrice    Float? // Renamed from 'cost'

  // Sales information
  salesAccount     String? // Ledger account name
  salesDescription String? // Sales description

  // Purchase information
  purchaseAccount     String? // Purchase account name
  purchaseDescription String? // Purchase description
  preferredVendor     String? // Preferred vendor name

  // Tax information
  intraStateTaxRate Float? // Intra-state tax percentage
  interStateTaxRate Float? // Inter-state tax percentage

  // Country-specific tax configuration
  taxCategoryId  String? @db.ObjectId // Link to tax category
  defaultTaxRate Float? // Default tax rate for this product

  // Legacy fields for backward compatibility
  price Float? // Deprecated, use sellingPrice
  cost  Float? // Deprecated, use costPrice

  deleted     Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt   DateTime? // Soft delete field - null when active, DateTime when deleted
  tenantId    String    @db.ObjectId
  createdById String?   @db.ObjectId
  updatedById String?   @db.ObjectId
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant              Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  category            ProductCategory?     @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  unit                Unit?                @relation(fields: [unitId], references: [id], onDelete: SetNull)
  taxCategory         TaxCategory?         @relation(fields: [taxCategoryId], references: [id], onDelete: SetNull)
  createdBy           User?                @relation("ProductCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy           User?                @relation("ProductUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  opportunityProducts OpportunityProduct[]
  quoteLines          QuoteLine[]
  packageLines        PackageLine[]
  priceBookEntries    PriceBookEntry[]
  customFields        CustomFieldValue[]   @relation("ProductCustomFields")

  @@index([tenantId])
  @@index([unitId])
}

model PriceBook {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?

  // Price book type and status
  isDefault Boolean         @default(false) // Whether this is the default price book
  status    PriceBookStatus @default(ACTIVE) // Price book status

  // Currency and regional settings
  currency String @default("USD") // Currency code (USD, EUR, etc.)

  // Effective date range
  effectiveFrom DateTime? // When this price book becomes effective
  effectiveTo   DateTime? // When this price book expires

  // Metadata
  tenantId    String    @db.ObjectId
  createdById String?   @db.ObjectId
  updatedById String?   @db.ObjectId
  deleted     Boolean   @default(false)
  deletedAt   DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant              Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy           User?                @relation("PriceBookCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy           User?                @relation("PriceBookUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  entries             PriceBookEntry[]
  opportunityProducts OpportunityProduct[]
  quoteLines          QuoteLine[]
  packageLines        PackageLine[]
  customFields        CustomFieldValue[]   @relation("PriceBookCustomFields")

  @@index([tenantId])
  @@index([isDefault])
  @@index([status])
  @@index([effectiveFrom])
  @@index([effectiveTo])
}

model PriceBookEntry {
  id String @id @default(auto()) @map("_id") @db.ObjectId

  // Core pricing information
  basePrice Float // Base price for this product in this price book
  listPrice Float? // Optional list price (MSRP)

  // Discount tiers (JSON structure for flexibility)
  discountTiers Json? // Array of discount tiers: [{ minQuantity: 10, discountPercent: 5 }, ...]

  // Effective date range for this specific entry
  effectiveFrom DateTime?
  effectiveTo   DateTime?

  // Status and flags
  isActive Boolean @default(true)

  // Cost information
  costPrice Float? // Cost price for margin calculations

  // Additional pricing attributes
  unitPrice       Float? // Price per unit (if different from base price)
  minimumQuantity Int? // Minimum order quantity
  maximumQuantity Int? // Maximum order quantity

  // Relationships
  priceBookId String @db.ObjectId
  productId   String @db.ObjectId
  tenantId    String @db.ObjectId

  // Metadata
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  priceBook PriceBook @relation(fields: [priceBookId], references: [id], onDelete: Cascade)
  product   Product   @relation(fields: [productId], references: [id], onDelete: Cascade)
  tenant    Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?     @relation("PriceBookEntryCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?     @relation("PriceBookEntryUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([priceBookId, productId]) // One entry per product per price book
  @@index([priceBookId])
  @@index([productId])
  @@index([tenantId])
  @@index([isActive])
  @@index([effectiveFrom])
  @@index([effectiveTo])
}

model Unit {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String // Unit code (e.g., "pcs", "kg")
  displayName String // User-friendly name (e.g., "Pieces", "Kilograms")
  tenantId    String    @db.ObjectId
  isSystem    Boolean   @default(false) // System vs custom units
  deleted     Boolean   @default(false) // Soft delete flag
  deletedAt   DateTime? // Soft delete field
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant       Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  products     Product[]
  packageLines PackageLine[]

  @@index([tenantId])
  @@index([isSystem])
}

model ProductCategory {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String // Category name (e.g., "Electronics", "Clothing")
  description String? // Optional description
  tenantId    String    @db.ObjectId
  isSystem    Boolean   @default(false) // System vs custom categories
  deleted     Boolean   @default(false) // Soft delete flag
  deletedAt   DateTime? // Soft delete field
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant   Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  products Product[]

  @@index([tenantId])
  @@index([isSystem])
}

model OpportunityProduct {
  id                   String                @id @default(auto()) @map("_id") @db.ObjectId
  quantity             Int
  unitPrice            Float
  discount             Float?                @default(0)
  totalPrice           Float
  customerBudget       Float? // Customer budget for this line item
  taxRate              Float?                @default(0) // Tax rate percentage
  description          String? // Additional description for this line item
  status               OpportunityLineStatus @default(ACTIVE) // Line item status
  expectedDeliveryDate DateTime? // Expected delivery date
  notes                String? // Notes/comments for this line item
  opportunityId        String                @db.ObjectId
  productId            String                @db.ObjectId
  priceBookId          String?               @db.ObjectId
  tenantId             String                @db.ObjectId
  createdById          String?               @db.ObjectId
  updatedById          String?               @db.ObjectId
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // Relations
  opportunity Opportunity @relation(fields: [opportunityId], references: [id], onDelete: Cascade)
  product     Product     @relation(fields: [productId], references: [id], onDelete: Cascade)
  priceBook   PriceBook?  @relation(fields: [priceBookId], references: [id], onDelete: SetNull)
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy   User?       @relation("OpportunityProductCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy   User?       @relation("OpportunityProductUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@index([opportunityId])
  @@index([productId])
  @@index([priceBookId])
  @@index([tenantId])
}

model OpportunityPackage {
  id                   String                @id @default(auto()) @map("_id") @db.ObjectId
  quantity             Int                   @default(1)
  unitPrice            Float? // Override package price if needed
  discount             Float?                @default(0) // Additional discount on package
  totalPrice           Float // Calculated total for this package instance
  customerBudget       Float? // Customer budget for this package
  description          String? // Additional description for this package instance
  status               OpportunityLineStatus @default(ACTIVE) // Package status in opportunity
  expectedDeliveryDate DateTime? // Expected delivery date for package
  notes                String? // Notes/comments for this package instance
  opportunityId        String                @db.ObjectId
  packageId            String                @db.ObjectId
  tenantId             String                @db.ObjectId
  createdById          String?               @db.ObjectId
  updatedById          String?               @db.ObjectId
  createdAt            DateTime              @default(now())
  updatedAt            DateTime              @updatedAt

  // Relations
  opportunity Opportunity @relation(fields: [opportunityId], references: [id], onDelete: Cascade)
  package     Package     @relation(fields: [packageId], references: [id], onDelete: Cascade)
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy   User?       @relation("OpportunityPackageCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy   User?       @relation("OpportunityPackageUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@index([opportunityId])
  @@index([packageId])
  @@index([tenantId])
}

model SupportTicket {
  id              String                @id @default(auto()) @map("_id") @db.ObjectId
  ticketNumber    String // Human-readable ticket number (e.g., TCK-000238)
  subject         String // Changed from title to subject
  description     String?
  status          SupportTicketStatus   @default(OPEN)
  priority        SupportTicketPriority @default(MEDIUM)
  type            TicketType?
  tags            String[] // Array of tags
  channel         TicketChannel         @default(EMAIL)
  language        String?
  creationSource  TicketCreationSource  @default(MANUAL)
  sourceSessionId String?               @db.ObjectId // ID of the originating inbox/chat session

  // Core relationships
  contactId  String  @db.ObjectId // Made required
  companyId  String? @db.ObjectId // Reference to company/account (renamed from accountId)
  assigneeId String? @db.ObjectId // Agent assigned to this ticket
  teamId     String? @db.ObjectId // Team responsible (using tenantId as team for now)
  tenantId   String  @db.ObjectId

  // Audit fields
  createdById String @db.ObjectId // Made required (who created the ticket)
  updatedById String @db.ObjectId // Last user who updated the ticket

  // Timestamps
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  closedAt         DateTime?
  firstResponseAt  DateTime?
  lastUserReplyAt  DateTime?
  lastAgentReplyAt DateTime?
  assignedAt       DateTime?
  resolvedAt       DateTime?

  // Resolution and AI fields
  resolutionType    TicketResolutionType?
  aiConfidenceScore Float? // 0 to 1
  aiModelVersion    String?

  // SLA and tracking
  slaViolation  Boolean  @default(false)
  internalNotes String[] // Array of internal-only notes

  // External integration
  externalRefId String? // Optional external system reference
  deleted       Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt     DateTime? // Soft delete field - null when active, DateTime when deleted
  auditLogId    String?   @db.ObjectId // Reference to audit log entry

  // Relations
  contact       Contact          @relation("ContactSupportTickets", fields: [contactId], references: [id], onDelete: Cascade)
  company       BusinessAccount? @relation("BusinessAccountSupportTickets", fields: [companyId], references: [id], onDelete: SetNull)
  assignee      User?            @relation("TicketAssignee", fields: [assigneeId], references: [id], onDelete: SetNull)
  tenant        Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy     User             @relation("SupportTicketCreatedBy", fields: [createdById], references: [id], onDelete: Cascade)
  lastUpdatedBy User             @relation("SupportTicketUpdatedBy", fields: [updatedById], references: [id], onDelete: Cascade)

  // New relations
  conversation     Conversation?      @relation(fields: [conversationId], references: [id])
  conversationId   String?            @db.ObjectId
  attachments      TicketAttachment[]
  customFields     CustomFieldValue[] @relation("TicketCustomFields")
  User             User?              @relation(fields: [userId], references: [id])
  userId           String?            @db.ObjectId
  CustomFieldValue CustomFieldValue[]

  // Approval Workflow Relations
  approvals          TicketApproval[]
  roadmapItems       RoadmapItem[]
  EmailProcessingLog EmailProcessingLog[]
  EmailMessage       EmailMessage[]

  @@index([tenantId])
  @@index([contactId])
  @@index([assigneeId])
  @@index([status])
  @@index([priority])
  @@index([createdAt])
  @@index([conversationId])
}

// Ticket Attachment model - Represents file attachments
model TicketAttachment {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  fileName     String
  originalName String
  fileSize     Int
  mimeType     String
  fileUrl      String
  isPublic     Boolean  @default(false) // Whether customer can see this attachment
  uploadedAt   DateTime @default(now())
  ticketId     String   @db.ObjectId
  uploadedById String   @db.ObjectId
  tenantId     String   @db.ObjectId

  // Relations
  ticket     SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  uploadedBy User          @relation("TicketAttachmentUploader", fields: [uploadedById], references: [id], onDelete: Cascade)
  tenant     Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  User       User?         @relation(fields: [userId], references: [id])
  userId     String?       @db.ObjectId

  @@index([ticketId])
}

model Campaign {
  id             String         @id @default(auto()) @map("_id") @db.ObjectId
  name           String
  type           CampaignType   @default(EMAIL)
  description    String?
  status         CampaignStatus @default(DRAFT)
  startDate      DateTime?      @default(now())
  endDate        DateTime?
  budget         Float?
  currency       String         @default("USD")
  targetAudience String?
  deleted        Boolean        @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt      DateTime? // Soft delete field - null when active, DateTime when deleted
  userId         String?        @db.ObjectId
  tenantId       String         @db.ObjectId
  createdById    String?        @db.ObjectId
  updatedById    String?        @db.ObjectId
  createdAt      DateTime       @default(now())
  updatedAt      DateTime       @updatedAt

  // Relations
  user             User?              @relation(fields: [userId], references: [id], onDelete: SetNull)
  tenant           Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy        User?              @relation("CampaignCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy        User?              @relation("CampaignUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  leads            Lead[]             @relation("CampaignLeads")
  opportunities    Opportunity[]      @relation("CampaignOpportunities")
  contacts         Contact[]          @relation("CampaignContacts")
  customFields     CustomFieldValue[] @relation("CampaignCustomFields")
  CustomFieldValue CustomFieldValue[]
  Contact          Contact[]
}

model CustomField {
  id                  String          @id @default(auto()) @map("_id") @db.ObjectId
  name                String // Internal field name (e.g., "custom_field_1")
  label               String // Display label (e.g., "Annual Revenue")
  description         String?
  type                CustomFieldType
  isRequired          Boolean         @default(false)
  defaultValue        String?
  placeholder         String?
  helpText            String?
  options             Json? // For SELECT and MULTI_SELECT types
  order               Int             @default(0)
  entityType          String // "Lead", "Contact", "Account", "Opportunity"
  isVisibleByDefault  Boolean         @default(true)
  isEditableByDefault Boolean         @default(false)

  // Advanced field properties
  minLength             Int?
  maxLength             Int?
  minValue              Float?
  maxValue              Float?
  decimalPlaces         Int?    @default(2) // For NUMBER, CURRENCY, PERCENT
  formulaExpression     String? // For FORMULA type
  lookupEntityType      String? // For LOOKUP type
  lookupField           String? // For LOOKUP type
  validationRule        String? // Custom validation expression
  validationMessage     String? // Message to show when validation fails
  displayFormat         String? // For DATE, DATETIME, TIME types
  isUnique              Boolean @default(false)
  isSearchable          Boolean @default(true)
  isSortable            Boolean @default(true)
  isFilterable          Boolean @default(true)
  groupName             String? // For grouping fields in sections
  displayOrder          Int     @default(0) // For ordering within groups
  conditionalVisibility Json? // Rules for conditional display
  maskType              String? // For input masking (phone, SSN, etc.)

  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  deleted     Boolean            @default(false) // Soft delete flag
  deletedAt   DateTime? // Soft delete field
  tenantId    String             @db.ObjectId
  createdById String?            @db.ObjectId
  updatedById String?            @db.ObjectId
  tenant      Tenant             @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy   User?              @relation("CustomFieldCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy   User?              @relation("CustomFieldUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  values      CustomFieldValue[]
  permissions FieldPermission[]  @relation("FieldPermissions")

  @@unique([tenantId, entityType, name])
  @@index([entityType])
}

model CustomFieldValue {
  id            String           @id @default(auto()) @map("_id") @db.ObjectId
  value         String? // All values stored as strings and converted as needed
  entityId      String // ID of the entity (Lead, Contact, etc.)
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  customFieldId String           @db.ObjectId
  createdById   String?          @db.ObjectId
  updatedById   String?          @db.ObjectId
  customField   CustomField      @relation(fields: [customFieldId], references: [id], onDelete: Cascade)
  createdBy     User?            @relation("CustomFieldValueCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?            @relation("CustomFieldValueUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  leadId        String?          @db.ObjectId
  lead          Lead?            @relation("LeadCustomFields", fields: [leadId], references: [id], onDelete: Cascade)
  accountId     String?          @db.ObjectId
  account       BusinessAccount? @relation("AccountCustomFields", fields: [accountId], references: [id], onDelete: Cascade)
  opportunityId String?          @db.ObjectId
  opportunity   Opportunity?     @relation("OpportunityCustomFields", fields: [opportunityId], references: [id], onDelete: Cascade)
  contactId     String?          @db.ObjectId
  contact       Contact?         @relation("ContactCustomFields", fields: [contactId], references: [id], onDelete: Cascade)
  campaignId    String?          @db.ObjectId
  campaign      Campaign?        @relation("CampaignCustomFields", fields: [campaignId], references: [id], onDelete: Cascade)
  taskId        String?          @db.ObjectId
  task          Task?            @relation("TaskCustomFields", fields: [taskId], references: [id], onDelete: Cascade)
  packageId     String?          @db.ObjectId
  package       Package?         @relation("PackageCustomFields", fields: [packageId], references: [id], onDelete: Cascade)

  pipelineStageId String?        @db.ObjectId
  pipelineStage   PipelineStage? @relation("PipelineStageCustomFields", fields: [pipelineStageId], references: [id], onDelete: Cascade)
  supportTicketId String?        @db.ObjectId
  supportTicket   SupportTicket? @relation("TicketCustomFields", fields: [supportTicketId], references: [id], onDelete: Cascade)
  productId       String?        @db.ObjectId
  product         Product?       @relation("ProductCustomFields", fields: [productId], references: [id], onDelete: Cascade)
  priceBookId     String?        @db.ObjectId
  priceBook       PriceBook?     @relation("PriceBookCustomFields", fields: [priceBookId], references: [id], onDelete: Cascade)
  Opportunity     Opportunity?   @relation(fields: [opportunityId], references: [id])
  Contact         Contact?       @relation(fields: [contactId], references: [id])
  Task            Task?          @relation(fields: [taskId], references: [id])
  PipelineStage   PipelineStage? @relation(fields: [pipelineStageId], references: [id])
  SupportTicket   SupportTicket? @relation(fields: [supportTicketId], references: [id])
  Campaign        Campaign?      @relation(fields: [campaignId], references: [id])

  @@unique([customFieldId, entityId])
  @@index([entityId])
}

// ==========================================
// Basic Quote Models (Simplified)
// ==========================================

model Quote {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  quoteNumber String  @unique
  name        String
  description String?

  // Status and lifecycle
  status  QuoteStatus @default(DRAFT)
  version Int         @default(1)

  // Relationships
  opportunityId String? @db.ObjectId
  accountId     String? @db.ObjectId
  contactId     String? @db.ObjectId

  // Pricing summary
  subtotal      Float    @default(0)
  totalDiscount Float    @default(0)
  totalTax      Float    @default(0)
  grandTotal    Float    @default(0)
  currency      Currency @default(USD)

  // Enhanced pricing fields
  quoteDiscountType  String? // 'PERCENTAGE' or 'FLAT'
  quoteDiscountValue Float   @default(0)
  intraStateTaxRate  Float   @default(0)
  interStateTaxRate  Float   @default(0)
  adjustments        Float   @default(0)
  isManualTotal      Boolean @default(false)
  manualTotalValue   Float?

  // Manual grand total override
  isManualGrandTotal Boolean @default(false)
  manualGrandTotal   Float?

  // Terms and conditions
  paymentTerms  String?   @default("Net 30")
  deliveryTerms String?
  validUntil    DateTime?

  // Quotation From fields (Seller/Company details)
  quotationFromCountry      String?
  quotationFromBusinessName String?
  quotationFromGSTIN        String?
  quotationFromAddress      String?
  quotationFromCity         String?
  quotationFromPostalCode   String?
  quotationFromState        String?

  // Quotation To fields (Customer/Buyer details)
  quotationToCountry      String?
  quotationToBusinessName String?
  quotationToGSTIN        String?
  quotationToAddress      String?
  quotationToCity         String?
  quotationToPostalCode   String?
  quotationToState        String?

  // Document generation
  generatedPdfUrl String?
  lastGeneratedAt DateTime?

  // Inventory integration
  inventoryCartId              String? // Cart ID from inventory service
  inventoryPaymentCollectionId String? // Payment collection ID from inventory service for advance payments

  // Payment tracking
  paymentRecordedAt DateTime? // When manual payment was recorded
  paymentStatus     String? // Payment status: "initiated", "recorded", "completed", etc.

  // Soft delete fields
  deleted   Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt DateTime? // Soft delete field - null when active, DateTime when deleted

  tenantId    String  @db.ObjectId
  ownerId     String  @db.ObjectId
  createdById String? @db.ObjectId
  updatedById String? @db.ObjectId

  // Relations
  opportunity Opportunity?     @relation(fields: [opportunityId], references: [id])
  account     BusinessAccount? @relation(fields: [accountId], references: [id])
  contact     Contact?         @relation(fields: [contactId], references: [id])
  tenant      Tenant           @relation(fields: [tenantId], references: [id])
  owner       User             @relation("QuoteOwner", fields: [ownerId], references: [id])
  createdBy   User?            @relation("QuoteCreatedBy", fields: [createdById], references: [id])
  updatedBy   User?            @relation("QuoteUpdatedBy", fields: [updatedById], references: [id])

  lines QuoteLine[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([tenantId])
  @@index([opportunityId])
  @@index([status])
  @@index([ownerId])
}

model QuoteLine {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  lineNumber  Int
  quantity    Int     @default(1)
  unitPrice   Float
  listPrice   Float?
  discount    Float   @default(0)
  lineTotal   Float
  description String?

  // Enhanced line item fields
  unitType      String? // Unit selector (pcs, kg, hrs, etc.)
  discountType  String? // 'PERCENTAGE' or 'FLAT'
  discountValue Float   @default(0)
  taxRate       Float? // Override tax rate per item
  subtotal      Float   @default(0) // Before discount/tax

  // Manual pricing override fields
  isManualPricing Boolean @default(false) // Enable manual line total override
  manualLineTotal Float? // Manual override for line total

  // Package-related fields
  itemType        String? @default("PRODUCT") // 'PRODUCT' or 'PACKAGE'
  packageId       String? @db.ObjectId // Reference to package if this is a package line
  packageName     String? // Package name for display
  packageContents Json? // Store package products as JSON for display

  quoteId     String  @db.ObjectId
  productId   String? @db.ObjectId
  priceBookId String? @db.ObjectId
  tenantId    String  @db.ObjectId

  // Relations
  quote     Quote      @relation(fields: [quoteId], references: [id], onDelete: Cascade)
  product   Product?   @relation(fields: [productId], references: [id])
  priceBook PriceBook? @relation(fields: [priceBookId], references: [id], onDelete: SetNull)
  package   Package?   @relation(fields: [packageId], references: [id])
  tenant    Tenant     @relation(fields: [tenantId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([quoteId])
  @@index([productId])
  @@index([priceBookId])
}

// ==========================================
// Package Models
// ==========================================

model Package {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  sku         String? // Auto-generated or manual SKU

  // Status and lifecycle
  status PackageStatus @default(ACTIVE)

  // Pricing and calculations
  subtotal           Float        @default(0) // Sum of all line item totals
  packageDiscount    Float        @default(0) // Package-level discount amount
  discountType       DiscountType @default(FLAT) // PERCENTAGE or FLAT
  discountValue      Float        @default(0) // Discount percentage or flat amount
  totalAfterDiscount Float        @default(0) // Subtotal minus package discount
  totalTax           Float        @default(0) // Tax amount
  grandTotal         Float        @default(0) // Final total after discount and tax

  // Manual override capability
  manualPriceOverride Boolean @default(false)
  manualPrice         Float? // Manual override price

  // Tax settings
  taxPercentage Float? @default(0) // Tax percentage for calculations

  // Country-specific tax configuration
  taxCategoryId  String? @db.ObjectId // Link to tax category
  defaultTaxRate Float? // Default tax rate for this package

  // Vendor/supplier associations
  preferredVendor String? // Preferred vendor name
  vendorId        String? @db.ObjectId // Future: vendor relationship

  // Metadata
  tenantId    String    @db.ObjectId
  createdById String?   @db.ObjectId
  updatedById String?   @db.ObjectId
  deleted     Boolean   @default(false) // Soft delete flag
  deletedAt   DateTime? // Soft delete field
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant        Tenant               @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  taxCategory   TaxCategory?         @relation(fields: [taxCategoryId], references: [id], onDelete: SetNull)
  createdBy     User?                @relation("PackageCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?                @relation("PackageUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  lines         PackageLine[]
  opportunities OpportunityPackage[] // Opportunities that include this package
  quoteLines    QuoteLine[] // Quote lines that reference this package
  customFields  CustomFieldValue[]   @relation("PackageCustomFields") // Custom field values for this package

  @@index([tenantId])
  @@index([status])
  @@index([createdAt])
  @@index([deleted])
}

model PackageLine {
  id         String @id @default(auto()) @map("_id") @db.ObjectId
  lineNumber Int // Order of line items

  // Product information
  productId   String? @db.ObjectId
  description String // Product/service description

  // Quantity and units
  quantity Int     @default(1)
  unitId   String? @db.ObjectId

  // Pricing
  unitPrice Float  @default(0)
  listPrice Float? // Original/list price for reference

  // Line-level discounts
  discount      Float        @default(0) // Discount amount
  discountType  DiscountType @default(FLAT) // PERCENTAGE or FLAT
  discountValue Float        @default(0) // Discount percentage or flat amount

  // Calculated totals
  lineTotal               Float @default(0) // Final line total after discount
  lineTotalBeforeDiscount Float @default(0) // Line total before discount

  // Relationships
  packageId   String  @db.ObjectId
  priceBookId String? @db.ObjectId
  tenantId    String  @db.ObjectId

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  package   Package    @relation(fields: [packageId], references: [id], onDelete: Cascade)
  product   Product?   @relation(fields: [productId], references: [id], onDelete: SetNull)
  priceBook PriceBook? @relation(fields: [priceBookId], references: [id], onDelete: SetNull)
  unit      Unit?      @relation(fields: [unitId], references: [id], onDelete: SetNull)
  tenant    Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([packageId])
  @@index([productId])
  @@index([priceBookId])
  @@index([tenantId])
}

// ==========================================
// Currency Configuration Models
// ==========================================

model FieldPermission {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  customFieldId String   @db.ObjectId
  canView       Boolean  @default(true)
  canEdit       Boolean  @default(false)
  tenantId      String   @db.ObjectId
  createdById   String?  @db.ObjectId
  updatedById   String?  @db.ObjectId
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  customField CustomField @relation("FieldPermissions", fields: [customFieldId], references: [id], onDelete: Cascade)
  tenant      Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy   User?       @relation("FieldPermissionCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy   User?       @relation("FieldPermissionUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([customFieldId, tenantId])
  @@index([customFieldId])
}

model DefaultFieldPermission {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  entityType  String
  fieldName   String
  canView     Boolean  @default(true)
  canEdit     Boolean  @default(false)
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?  @relation("DefaultFieldPermissionCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?  @relation("DefaultFieldPermissionUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([entityType, fieldName, tenantId])
  @@index([entityType, fieldName])
}

// Inbox model - Represents a communication channel
model Inbox {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  type        InboxType
  icon        String?
  color       String?
  settings    Json? // Channel-specific settings
  isActive    Boolean   @default(true) // Active status flag - true when active, false when inactive
  deleted     Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt   DateTime? // Soft delete field - null when active, DateTime when deleted
  tenantId    String    @db.ObjectId
  createdById String?   @db.ObjectId
  updatedById String?   @db.ObjectId
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant          Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy       User?             @relation("InboxCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy       User?             @relation("InboxUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  conversations   Conversation[]
  agents          InboxAgent[]
  WhatsAppMessage WhatsAppMessage[]
}

// InboxAgent model - Join table for Inbox and User (agents)
model InboxAgent {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  inboxId   String   @db.ObjectId
  userId    String   @db.ObjectId
  role      String   @default("agent")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  inbox Inbox @relation(fields: [inboxId], references: [id], onDelete: Cascade)
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([inboxId, userId])
  @@index([inboxId])
  @@index([userId])
}

// Conversation model - Represents a thread of messages
model Conversation {
  id            String             @id @default(auto()) @map("_id") @db.ObjectId
  subject       String?
  status        ConversationStatus @default(OPEN)
  priority      Int                @default(0) // 0 = normal, 1 = high, 2 = urgent
  isStarred     Boolean            @default(false)
  messageCount  Int                @default(0)
  lastMessageAt DateTime?
  snoozedUntil  DateTime?
  meta          Json?
  createdAt     DateTime           @default(now())
  updatedAt     DateTime           @updatedAt
  inboxId       String?            @db.ObjectId
  assignedToId  String?            @db.ObjectId
  contactId     String?            @db.ObjectId
  accountId     String?            @db.ObjectId
  tenantId      String             @db.ObjectId
  createdById   String?            @db.ObjectId
  updatedById   String?            @db.ObjectId

  // Relations
  inbox           Inbox?              @relation(fields: [inboxId], references: [id], onDelete: Cascade)
  assignedTo      User?               @relation("AssignedConversations", fields: [assignedToId], references: [id], onDelete: SetNull)
  contact         Contact?            @relation("ContactConversations", fields: [contactId], references: [id], onDelete: SetNull)
  account         BusinessAccount?    @relation("AccountConversations", fields: [accountId], references: [id], onDelete: SetNull)
  tenant          Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy       User?               @relation("ConversationCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy       User?               @relation("ConversationUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  messages        Message[]
  participants    Participant[]
  labels          ConversationLabel[]
  SupportTicket   SupportTicket[]
  WhatsAppMessage WhatsAppMessage[]
}

model Message {
  id             String        @id @default(auto()) @map("_id") @db.ObjectId
  content        String
  contentType    String        @default("text")
  status         MessageStatus @default(SENT)
  isIncoming     Boolean       @default(false)
  attachments    Json?
  meta           Json?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  conversationId String        @db.ObjectId
  senderId       String?       @db.ObjectId
  tenantId       String        @db.ObjectId

  // Relations
  conversation    Conversation      @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender          User?             @relation("SentMessages", fields: [senderId], references: [id], onDelete: SetNull)
  tenant          Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  whatsappMessage WhatsAppMessage[]
}

// WhatsApp Message model - Stores all WhatsApp messages with full metadata
model WhatsAppMessage {
  id                String                @id @default(auto()) @map("_id") @db.ObjectId
  whatsappMessageId String // WhatsApp's unique message ID
  whatsappTimestamp String? // WhatsApp timestamp as string
  messageType       WhatsAppMessageType   @default(TEXT)
  content           String? // Text content for text messages
  mediaUrl          String? // URL for media messages
  mediaId           String? // WhatsApp media ID
  mimeType          String? // MIME type for media
  filename          String? // Original filename for documents
  caption           String? // Caption for media messages
  fromPhoneNumber   String // Sender's phone number
  toPhoneNumber     String? // Recipient's phone number (for outgoing)
  profileName       String? // Sender's profile name from WhatsApp
  isIncoming        Boolean               @default(true)
  status            WhatsAppMessageStatus @default(RECEIVED)

  // Context and reply information
  contextMessageId String? // ID of message being replied to
  contextFrom      String? // Phone number of original message sender
  contextId        String? // WhatsApp context ID

  // Location data (for location messages)
  locationLatitude  Float?
  locationLongitude Float?
  locationName      String?
  locationAddress   String?

  // Contact data (for contact messages)
  contactName  String?
  contactPhone String?
  contactEmail String?

  // Interactive message data
  interactiveType String? // button, list, etc.
  interactiveData Json? // Button/list response data

  // System and metadata
  rawWebhookData     Json? // Complete webhook payload for debugging
  processingStatus   WhatsAppProcessingStatus @default(PENDING)
  processingError    String? // Error message if processing failed
  processingAttempts Int                      @default(0)

  // Tenant and relationships
  tenantId       String  @db.ObjectId
  inboxId        String? @db.ObjectId // Linked inbox if found
  conversationId String? @db.ObjectId // Linked conversation if created
  messageId      String? @db.ObjectId // Linked Message record if created
  contactId      String? @db.ObjectId // Linked contact if found/created

  // Status timestamps
  sentAt      DateTime? // When message was sent (for outgoing messages)
  deliveredAt DateTime? // When message was delivered
  readAt      DateTime? // When message was read by recipient

  // Audit fields
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  processedAt DateTime? // When message was successfully processed

  // Relations
  tenant       Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  inbox        Inbox?        @relation(fields: [inboxId], references: [id], onDelete: SetNull)
  conversation Conversation? @relation(fields: [conversationId], references: [id], onDelete: SetNull)
  message      Message?      @relation(fields: [messageId], references: [id], onDelete: SetNull)
  contact      Contact?      @relation(fields: [contactId], references: [id], onDelete: SetNull)

  @@index([tenantId])
  @@index([fromPhoneNumber])
  @@index([toPhoneNumber])
  @@index([inboxId])
  @@index([conversationId])
  @@index([processingStatus])
  @@index([createdAt])
  @@index([isIncoming])
}

// CRM Email Configuration model - Stores multiple email configurations per tenant
model CRMEmailConfig {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  tenantId    String  @db.ObjectId
  name        String // User-friendly name for this configuration
  description String? // Optional description

  // Email Configuration
  isEnabled  Boolean                @default(true)
  provider   CRMEmailProvider       @default(FLINKK_MAIL)
  fromName   String // Sender name
  fromEmail  String // Sender email address
  status     CRMCommunicationStatus @default(INACTIVE)
  lastTested DateTime?
  lastSynced DateTime?              @default(now())
  isDefault  Boolean                @default(false) // Mark as default configuration

  // OAuth Email Provider Settings (Outlook/Gmail)
  accessToken      String?
  refreshToken     String?
  tokenExpiry      DateTime?
  connectedAccount String?

  // SMTP Settings (Flinkk Mail)
  smtpHost     String?
  smtpPort     Int?
  smtpUsername String?
  smtpPassword String?
  smtpTls      Boolean @default(true)

  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId

  // Relations
  tenant       Tenant         @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy    User?          @relation("CRMEmailConfigCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy    User?          @relation("CRMEmailConfigUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  EmailMessage EmailMessage[]

  @@index([tenantId])
  @@index([isEnabled])
  @@index([isDefault])
  @@index([provider])
  @@index([fromEmail])
}

model Participant {
  id             String    @id @default(auto()) @map("_id") @db.ObjectId
  role           String    @default("member") // member, admin, observer
  joinedAt       DateTime  @default(now())
  lastReadAt     DateTime?
  conversationId String    @db.ObjectId
  userId         String    @db.ObjectId

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  user         User         @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([conversationId, userId])
}

// Label model - Tags for organizing conversations
model Label {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  color       String?
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant        Tenant              @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy     User?               @relation("LabelCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?               @relation("LabelUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  conversations ConversationLabel[]
}

// Join table for Conversation and Label
model ConversationLabel {
  id             String   @id @default(auto()) @map("_id") @db.ObjectId
  conversationId String   @db.ObjectId
  labelId        String   @db.ObjectId
  createdAt      DateTime @default(now())

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  label        Label        @relation(fields: [labelId], references: [id], onDelete: Cascade)

  @@unique([conversationId, labelId])
}

// Feedback model - For collecting and managing user feedback
model Feedback {
  id          String           @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String
  type        FeedbackType     @default(FEATURE)
  status      FeedbackStatus   @default(SUBMITTED)
  priority    FeedbackPriority @default(MEDIUM)
  isPublic    Boolean          @default(true)
  isArchived  Boolean          @default(false)
  deleted     Boolean          @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt   DateTime? // Soft delete field - null when active, DateTime when deleted
  voteCount   Int              @default(0)
  tags        String? // Comma-separated tags
  tenantId    String           @db.ObjectId
  createdById String?          @db.ObjectId
  updatedById String?          @db.ObjectId
  accountId   String?          @db.ObjectId
  contactId   String?          @db.ObjectId
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  tenant    Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?             @relation("FeedbackCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?             @relation("FeedbackUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  account   BusinessAccount?  @relation("BusinessAccountFeedback", fields: [accountId], references: [id], onDelete: SetNull)
  contact   Contact?          @relation("ContactFeedback", fields: [contactId], references: [id], onDelete: SetNull)
  votes     FeedbackVote[]
  comments  FeedbackComment[]

  @@index([type, status, isPublic, isArchived])
  @@index([tenantId])
}

// FeedbackVote model - For tracking votes on feedback items
model FeedbackVote {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  feedbackId String   @db.ObjectId
  userId     String   @db.ObjectId
  createdAt  DateTime @default(now())

  // Relations
  feedback Feedback @relation(fields: [feedbackId], references: [id], onDelete: Cascade)
  user     User     @relation("UserFeedbackVotes", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([feedbackId, userId])
  @@index([feedbackId])
}

// FeedbackComment model - For comments on feedback items
model FeedbackComment {
  id         String   @id @default(auto()) @map("_id") @db.ObjectId
  content    String
  isInternal Boolean  @default(false) // If true, only visible to tenant members
  feedbackId String   @db.ObjectId
  userId     String   @db.ObjectId
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  feedback Feedback @relation(fields: [feedbackId], references: [id], onDelete: Cascade)
  user     User     @relation("UserFeedbackComments", fields: [userId], references: [id], onDelete: Cascade)

  @@index([feedbackId])
  @@index([userId])
}

model Note {
  id                String    @id @default(auto()) @map("_id") @db.ObjectId
  title             String
  content           String
  reference_modal   String
  reference_modalId String    @db.ObjectId
  deleted           Boolean   @default(false) // Soft delete flag - false when active, true when deleted
  deletedAt         DateTime? // Soft delete field - null when active, DateTime when deleted
  tenantId          String    @db.ObjectId
  authorId          String    @db.ObjectId
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  author User   @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([authorId])
  @@index([reference_modalId])
}

model ActivityLog {
  id               String        @id @default(auto()) @map("_id") @db.ObjectId
  title            String
  description      String?
  activity_type    ActivityType?
  activity_type_id String?       @db.ObjectId
  action           ActionType? // CRUD action on the related entity

  related_to_type RelatedToType
  related_to_id   String        @db.ObjectId
  user_id         String?       @db.ObjectId
  tenantId        String        @db.ObjectId

  visibility ActivityVisibility @default(INTERNAL)

  activity_time DateTime @default(now())
  created_at    DateTime @default(now())
  updated_at    DateTime @updatedAt

  // Relations
  user   User?  @relation(fields: [user_id], references: [id], onDelete: SetNull)
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([related_to_type, related_to_id])
  @@index([user_id])
  @@index([tenantId])
}

// ==========================================
// Approval Workflow Models
// ==========================================

// Ticket Approval model - Tracks approval requests for feature tickets
model TicketApproval {
  id          String         @id @default(auto()) @map("_id") @db.ObjectId
  status      ApprovalStatus @default(PENDING)
  requestedAt DateTime       @default(now())
  reviewedAt  DateTime?
  comments    String? // Approval/rejection comments

  // Relationships
  ticketId    String  @db.ObjectId
  requesterId String  @db.ObjectId // User who requested approval
  reviewerId  String? @db.ObjectId // User who reviewed the approval
  tenantId    String  @db.ObjectId

  // Relations
  ticket    SupportTicket @relation(fields: [ticketId], references: [id], onDelete: Cascade)
  requester User          @relation("ApprovalRequester", fields: [requesterId], references: [id], onDelete: Cascade)
  reviewer  User?         @relation("ApprovalReviewer", fields: [reviewerId], references: [id], onDelete: SetNull)
  tenant    Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  RoadmapItem RoadmapItem[]

  @@index([ticketId])
  @@index([status])
  @@index([tenantId])
}

// Roadmap Item model - Stores approved feature requests
model RoadmapItem {
  id              String          @id @default(auto()) @map("_id") @db.ObjectId
  title           String
  description     String?
  status          RoadmapStatus   @default(PLANNED)
  priority        RoadmapPriority @default(MEDIUM)
  category        String? // Feature category (e.g., "UI/UX", "API", "Integration")
  estimatedEffort String? // Effort estimation (e.g., "Small", "Medium", "Large")
  targetQuarter   String? // Target quarter for completion (e.g., "Q1 2024")
  targetDate      DateTime? // Specific target date
  completedAt     DateTime? // When the feature was completed

  // Relationships
  originalTicketId String? @db.ObjectId // Reference to the original feature request ticket
  approvalId       String? @db.ObjectId // Reference to the approval that created this roadmap item
  assigneeId       String? @db.ObjectId // Developer/team assigned to this feature
  tenantId         String  @db.ObjectId

  // Relations
  originalTicket SupportTicket?  @relation(fields: [originalTicketId], references: [id], onDelete: SetNull)
  approval       TicketApproval? @relation(fields: [approvalId], references: [id], onDelete: SetNull)
  assignee       User?           @relation("RoadmapAssignee", fields: [assigneeId], references: [id], onDelete: SetNull)
  tenant         Tenant          @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([status])
  @@index([priority])
  @@index([tenantId])
  @@index([originalTicketId])
}

// ==========================================
// AI Chatbot Models (Agent/Thread/Interaction)
// ==========================================

// Agent model - Represents an AI agent with specific capabilities
model Agent {
  id           String    @id @default(auto()) @map("_id") @db.ObjectId
  name         String
  description  String?
  type         String    @default("assistant") // assistant, specialist, etc.
  capabilities Json? // AI-specific capabilities and settings
  model        String? // AI model identifier (e.g., "gpt-4", "claude-3")
  systemPrompt String? // System prompt for the AI agent
  settings     Json? // Agent-specific settings (temperature, max_tokens, etc.)
  isActive     Boolean   @default(true)
  deleted      Boolean   @default(false)
  deletedAt    DateTime?
  tenantId     String    @db.ObjectId
  createdById  String?   @db.ObjectId
  updatedById  String?   @db.ObjectId
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?    @relation("AgentCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?    @relation("AgentUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  threads   Thread[]

  @@index([tenantId])
  @@index([isActive])
  @@index([type])
}

// Thread model - Represents an AI chat thread (similar to Conversation)
model Thread {
  id            String       @id @default(auto()) @map("_id") @db.ObjectId
  subject       String?
  status        ThreadStatus @default(ACTIVE)
  priority      Int          @default(0) // 0 = normal, 1 = high, 2 = urgent
  isStarred     Boolean      @default(false)
  lastMessageAt DateTime?
  meta          Json? // Additional metadata
  sessionId     String? // Session identifier for tracking
  agentId       String       @db.ObjectId
  userId        String?      @db.ObjectId // User who started the thread
  tenantId      String       @db.ObjectId
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  // Relations
  agent        Agent         @relation(fields: [agentId], references: [id], onDelete: Cascade)
  user         User?         @relation("UserThreads", fields: [userId], references: [id], onDelete: SetNull)
  tenant       Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  interactions Interaction[]

  @@index([tenantId])
  @@index([agentId])
  @@index([userId])
  @@index([status])
  @@index([sessionId])
}

// Interaction model - Represents a message in an AI chat thread (similar to Message)
model Interaction {
  id          String          @id @default(auto()) @map("_id") @db.ObjectId
  content     String
  contentType String          @default("text")
  role        InteractionRole // user, assistant, system
  status      MessageStatus   @default(SENT)
  attachments Json?
  meta        Json? // AI-specific metadata (tokens, model used, etc.)
  threadId    String          @db.ObjectId
  userId      String?         @db.ObjectId // User who sent the message (null for AI responses)
  tenantId    String          @db.ObjectId
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Relations
  thread Thread @relation(fields: [threadId], references: [id], onDelete: Cascade)
  user   User?  @relation("UserInteractions", fields: [userId], references: [id], onDelete: SetNull)
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([threadId])
  @@index([userId])
  @@index([role])
  @@index([createdAt])
}

// ==========================================
// Multi-Agent AI Action Models
// ==========================================

model EmailDraft {
  id            String    @id @default(auto()) @map("_id") @db.ObjectId
  from          String?
  to            String[]
  subject       String? /// @encrypted
  htmlContent   String? /// @encrypted
  attachments   Json?
  scheduledTime DateTime?
  status        String    @default("draft") // draft, sent, scheduled, failed
  tenantId      String?   @db.ObjectId
  deleted       Boolean   @default(false)

  // Reference to source (Lead, Note, etc.)
  reference_modal   String // "Lead", "Note", "Opportunity"
  reference_modalId String // ID of the referenced entity

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([reference_modalId])
  @@index([status])
}

// Email Processing Log - Track all incoming emails and their processing status
model EmailProcessingLog {
  id                 String                @id @default(auto()) @map("_id") @db.ObjectId
  messageId          String // Email message ID from the email provider
  fromEmail          String
  fromName           String?
  toEmail            String
  subject            String
  bodyText           String? // Plain text version of email body
  bodyHtml           String? // HTML version of email body
  attachmentCount    Int                   @default(0)
  attachmentData     Json? // Metadata about attachments
  processingStatus   EmailProcessingStatus @default(PENDING)
  processingError    String? // Error message if processing failed
  ticketId           String?               @db.ObjectId // Created ticket ID if successful
  contactId          String?               @db.ObjectId // Associated contact ID
  tenantId           String?               @db.ObjectId // Determined tenant ID
  spamScore          Float? // Spam detection score (0-1)
  isSpam             Boolean               @default(false)
  rawEmailData       Json? // Raw email data for debugging
  processingAttempts Int                   @default(0)
  lastProcessedAt    DateTime?
  createdAt          DateTime              @default(now())
  updatedAt          DateTime              @updatedAt

  // Relations
  ticket  SupportTicket? @relation(fields: [ticketId], references: [id], onDelete: SetNull)
  contact Contact?       @relation(fields: [contactId], references: [id], onDelete: SetNull)
  tenant  Tenant?        @relation(fields: [tenantId], references: [id], onDelete: SetNull)

  @@index([tenantId])
  @@index([fromEmail])
  @@index([toEmail])
  @@index([processingStatus])
  @@index([createdAt])
  @@index([ticketId])
}

// Comprehensive Email Message model - Stores all email messages (sent, received, drafts)
model EmailMessage {
  id        String  @id @default(auto()) @map("_id") @db.ObjectId
  messageId String? // External message ID from email provider
  threadId  String? // Email thread/conversation ID

  // Email content
  subject  String? /// @encrypted
  bodyText String? /// @encrypted // Plain text version
  bodyHtml String? /// @encrypted // HTML version

  // Sender and recipients
  fromEmail    String
  fromName     String?
  toEmails     String[] // Array of recipient emails
  ccEmails     String[] @default([]) // CC recipients
  bccEmails    String[] @default([]) // BCC recipients
  replyToEmail String? // Reply-to address
  rawData      Json? // Raw data from email provider

  // Email metadata
  type     EmailMessageType   @default(SENT) // SENT, RECEIVED, DRAFT
  status   EmailMessageStatus @default(PENDING) // PENDING, SENT, DELIVERED, READ, FAILED, DRAFT
  priority EmailPriority      @default(NORMAL) // LOW, NORMAL, HIGH, URGENT

  // Attachments
  attachmentCount Int      @default(0)
  attachmentData  Json? // Metadata about attachments
  attachmentUrls  String[] @default([]) // URLs to attachment files

  // Scheduling and delivery
  scheduledAt DateTime? // For scheduled emails
  sentAt      DateTime? // When email was actually sent
  deliveredAt DateTime? // When email was delivered
  readAt      DateTime? // When email was read (if tracking enabled)
  receivedAt  DateTime? // When email was read (if tracking enabled)

  // Configuration and tracking
  configId        String? @db.ObjectId // Email configuration used
  provider        String? // Email provider used (outlook, gmail, flinkk_mail)
  trackingEnabled Boolean @default(false)
  trackingData    Json? // Tracking information (opens, clicks, etc.)

  // Relations and references
  tenantId      String  @db.ObjectId
  userId        String? @db.ObjectId // User who sent/received the email
  contactId     String? @db.ObjectId // Associated contact
  leadId        String? @db.ObjectId // Associated lead
  opportunityId String? @db.ObjectId // Associated opportunity
  ticketId      String? @db.ObjectId // Associated support ticket

  // Soft delete and audit
  deleted   Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant      Tenant          @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user        User?           @relation("EmailMessageUser", fields: [userId], references: [id], onDelete: SetNull)
  contact     Contact?        @relation(fields: [contactId], references: [id], onDelete: SetNull)
  lead        Lead?           @relation(fields: [leadId], references: [id], onDelete: SetNull)
  opportunity Opportunity?    @relation(fields: [opportunityId], references: [id], onDelete: SetNull)
  ticket      SupportTicket?  @relation(fields: [ticketId], references: [id], onDelete: SetNull)
  config      CRMEmailConfig? @relation(fields: [configId], references: [id], onDelete: SetNull)

  // Indexes for efficient querying
  @@index([tenantId])
  @@index([userId])
  @@index([fromEmail])
  @@index([toEmails])
  @@index([type])
  @@index([status])
  @@index([createdAt])
  @@index([sentAt])
  @@index([threadId])
  @@index([contactId])
  @@index([leadId])
  @@index([opportunityId])
  @@index([ticketId])
  @@index([deleted])
  @@index([tenantId, type, status])
  @@index([tenantId, fromEmail, createdAt])
  @@index([tenantId, deleted, createdAt])
}

// API Key Management - For external integrations
model ApiKey {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String // Human-readable name for the API key
  description String? // Optional description of what this key is used for
  keyHash     String    @unique // Hashed version of the API key for security
  keyPrefix   String // First few characters of the key for identification (e.g., "flk_")
  isActive    Boolean   @default(true)
  lastUsedAt  DateTime?
  expiresAt   DateTime? // Optional expiration date

  // Permissions and scoping
  permissions Json // JSON object defining what this key can access
  ipWhitelist String[] // Optional IP address whitelist

  // Rate limiting
  rateLimit Int? @default(1000) // Requests per hour

  // Audit fields
  tenantId    String   @db.ObjectId
  createdById String   @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User   @relation("ApiKeyCreatedBy", fields: [createdById], references: [id], onDelete: Cascade)
  updatedBy User?  @relation("ApiKeyUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@index([tenantId])
  @@index([isActive])
}

// Webhook Configuration - For outbound notifications
model WebhookConfig {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  name        String // Human-readable name for the webhook
  description String? // Optional description
  url         String // The webhook endpoint URL
  isActive    Boolean @default(true)

  // Event configuration
  events String[] // Array of events to listen for (e.g., ["ticket.created", "ticket.updated"])

  // Security
  secret String? // Optional webhook secret for signature verification

  // Retry configuration
  retryEnabled Boolean @default(true)
  maxRetries   Int     @default(3)
  retryDelay   Int     @default(60) // Seconds between retries

  // Headers and authentication
  headers Json? // Custom headers to send with webhook requests

  // Audit fields
  tenantId    String   @db.ObjectId
  createdById String   @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant     Tenant            @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy  User              @relation("WebhookConfigCreatedBy", fields: [createdById], references: [id], onDelete: Cascade)
  updatedBy  User?             @relation("WebhookConfigUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  deliveries WebhookDelivery[]

  @@index([tenantId])
  @@index([isActive])
}

// Webhook Delivery Log - Track webhook delivery attempts
model WebhookDelivery {
  id            String                @id @default(auto()) @map("_id") @db.ObjectId
  webhookId     String                @db.ObjectId
  retryCount    Int                   @default(0)
  eventType     String // The event that triggered this webhook
  payload       Json // The payload that was sent
  httpStatus    Int? // HTTP status code received
  responseBody  String? // Response body from the webhook endpoint
  responseTime  Int? // Response time in milliseconds
  status        WebhookDeliveryStatus @default(PENDING)
  attemptNumber Int                   @default(1)
  nextRetryAt   DateTime?
  errorMessage  String?

  // Audit fields
  tenantId  String   @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant  Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  webhook WebhookConfig @relation(fields: [webhookId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([webhookId])
  @@index([status])
  @@index([eventType])
  @@index([createdAt])
}

model TaskItem {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  priority    String    @default("medium") // low, medium, high, urgent
  status      String    @default("pending") // pending, in_progress, completed, cancelled
  dueDate     DateTime?
  assigneeId  String?   @db.ObjectId
  tenantId    String?   @db.ObjectId
  deleted     Boolean   @default(false)

  // Reference to source
  reference_modal   String
  reference_modalId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant   Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  assignee User?   @relation("TaskItemAssignee", fields: [assigneeId], references: [id], onDelete: SetNull)

  @@index([tenantId])
  @@index([assigneeId])
  @@index([reference_modalId])
  @@index([status])
}

model EventItem {
  id          String   @id @default(auto()) @map("_id") @db.ObjectId
  title       String
  description String?
  startTime   DateTime
  endTime     DateTime
  location    String?
  attendees   String[]
  eventType   String   @default("meeting") // meeting, call, reminder, deadline
  status      String   @default("scheduled") // scheduled, completed, cancelled
  tenantId    String?  @db.ObjectId
  deleted     Boolean  @default(false)

  // Reference to source
  reference_modal   String
  reference_modalId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant Tenant? @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([reference_modalId])
  @@index([status])
  @@index([startTime])
}

// ==========================================
// Help Portal Models
// ==========================================

// Help Portal Category model - Organizes help content
model HelpCategory {
  id          String    @id @default(auto()) @map("_id") @db.ObjectId
  name        String
  description String?
  slug        String // URL-friendly identifier
  icon        String? // Icon name or URL
  color       String? // Hex color code
  sortOrder   Int       @default(0)
  isActive    Boolean   @default(true)
  deleted     Boolean   @default(false)
  deletedAt   DateTime?
  tenantId    String    @db.ObjectId
  createdById String?   @db.ObjectId
  updatedById String?   @db.ObjectId
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  tenant    Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?         @relation("HelpCategoryCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?         @relation("HelpCategoryUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  articles  HelpArticle[]

  @@unique([slug, tenantId]) // Slug must be unique within tenant
  @@index([tenantId])
  @@index([isActive])
  @@index([sortOrder])
}

// Help Portal Article model - Documentation and guides
model HelpArticle {
  id             String            @id @default(auto()) @map("_id") @db.ObjectId
  title          String
  content        String // Rich text content
  excerpt        String? // Short description
  slug           String // URL-friendly identifier
  status         HelpContentStatus @default(DRAFT)
  featured       Boolean           @default(false)
  viewCount      Int               @default(0)
  sortOrder      Int               @default(0)
  tags           String[] // Array of tags
  seoTitle       String? // SEO meta title
  seoDescription String? // SEO meta description
  deleted        Boolean           @default(false)
  deletedAt      DateTime?
  publishedAt    DateTime?
  categoryId     String?           @db.ObjectId
  tenantId       String            @db.ObjectId
  authorId       String            @db.ObjectId
  updatedById    String?           @db.ObjectId
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt

  // Relations
  tenant      Tenant           @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  category    HelpCategory?    @relation(fields: [categoryId], references: [id], onDelete: SetNull)
  author      User             @relation("HelpArticleAuthor", fields: [authorId], references: [id], onDelete: Cascade)
  updatedBy   User?            @relation("HelpArticleUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  attachments HelpAttachment[]
  votes       HelpVote[]

  @@unique([slug, tenantId]) // Slug must be unique within tenant
  @@index([tenantId])
  @@index([categoryId])
  @@index([status])
  @@index([featured])
  @@index([publishedAt])
}

// Help Portal Attachment model - Files attached to articles
model HelpAttachment {
  id           String    @id @default(auto()) @map("_id") @db.ObjectId
  fileName     String
  originalName String
  fileSize     Int
  mimeType     String
  fileUrl      String
  description  String?
  sortOrder    Int       @default(0)
  deleted      Boolean   @default(false)
  deletedAt    DateTime?
  articleId    String    @db.ObjectId
  tenantId     String    @db.ObjectId
  uploadedById String    @db.ObjectId
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  tenant     Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  article    HelpArticle @relation(fields: [articleId], references: [id], onDelete: Cascade)
  uploadedBy User        @relation("HelpAttachmentUploader", fields: [uploadedById], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([articleId])
}

// Help Portal Vote model - User feedback on articles
model HelpVote {
  id        String   @id @default(auto()) @map("_id") @db.ObjectId
  voteType  VoteType // HELPFUL or NOT_HELPFUL
  comment   String? // Optional feedback comment
  articleId String?  @db.ObjectId
  tenantId  String   @db.ObjectId
  userId    String?  @db.ObjectId // Null for anonymous votes
  sessionId String? // For anonymous user tracking
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant  Tenant       @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  user    User?        @relation("HelpVoteUser", fields: [userId], references: [id], onDelete: SetNull)
  article HelpArticle? @relation(fields: [articleId], references: [id], onDelete: Cascade)

  @@index([tenantId])
  @@index([articleId])
  @@index([userId])
}

// Help Portal Settings model - Tenant-specific configuration
model HelpPortalSettings {
  id                   String   @id @default(auto()) @map("_id") @db.ObjectId
  portalTitle          String   @default("Help Center")
  portalDescription    String?
  logoUrl              String?
  primaryColor         String?  @default("#0070f3")
  secondaryColor       String?  @default("#666666")
  customCSS            String? // Custom CSS for branding
  contactEmail         String?
  contactPhone         String?
  showContactForm      Boolean  @default(true)
  showSearch           Boolean  @default(true)
  showVoting           Boolean  @default(true)
  allowAnonymous       Boolean  @default(true)
  seoEnabled           Boolean  @default(true)
  analyticsCode        String? // Google Analytics or other tracking code
  subdomainSlug        String? // Custom subdomain slug (e.g., company.help.flinkk.com)
  subdomainVerified    Boolean  @default(false) // Whether subdomain is verified with Vercel
  customDomain         String? // Custom domain (e.g., help.company.com)
  customDomainVerified Boolean  @default(false) // Whether custom domain is verified with Vercel
  customDomainType     String?  @default("CNAME") // CNAME or ALIAS
  customDomainRecords  Json[] // DNS records for custom domain
  isPublic             Boolean  @default(true) // Whether portal is publicly accessible
  tenantId             String   @unique @db.ObjectId
  updatedById          String?  @db.ObjectId
  createdAt            DateTime @default(now())
  updatedAt            DateTime @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  updatedBy User?  @relation("HelpPortalSettingsUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
}

// Help Portal Layout Configuration - Configurable header and footer links
model HelpPortalConfig {
  id            String   @id @default(auto()) @map("_id") @db.ObjectId
  configuration Json // JSON object containing logo, headerLinks, footerSections
  tenantId      String   @unique @db.ObjectId
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
}

// ==========================================
// Mail Configuration Models
// ==========================================

model MailConfiguration {
  id           String  @id @default(auto()) @map("_id") @db.ObjectId
  mailUrl      String // Mail server URL (e.g., smtp.gmail.com)
  mailTenantId String // Mail tenant/account identifier
  isActive     Boolean @default(true)

  // Audit fields
  tenantId  String   @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId]) // Only one mail configuration per tenant
  @@index([isActive])
}

// ==========================================
// Inventory Configuration Models
// ==========================================

model InventoryConfiguration {
  id       String  @id @default(auto()) @map("_id") @db.ObjectId
  apiUrl   String // Inventory service URL (e.g., https://inventory.example.com)
  token    String // Authentication token for inventory service
  isActive Boolean @default(true)

  // Connection status tracking
  lastVerifiedAt      DateTime? // Last successful connection verification
  verificationStatus  String? // "connected", "disconnected", "error"
  verificationMessage String? // Last verification message

  // Audit fields
  tenantId  String   @db.ObjectId
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  tenant Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  @@unique([tenantId]) // Only one inventory configuration per tenant
  @@index([isActive])
  @@index([verificationStatus])
}

// ==========================================
// Enums
// ==========================================

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum AccountType {
  B2B
  B2C
  FAMILY
  CORPORATE
  CUSTOMER
  PROSPECT
  PARTNER
  VENDOR
  OTHER
}

enum ActivityType {
  CALL
  EMAIL
  NOTE
  MEETING
  WHATSAPP
  TASK
  OTHER
}

enum SupportTicketStatus {
  OPEN
  IN_PROGRESS
  WAITING_ON_CUSTOMER
  WAITING_ON_THIRD_PARTY
  RESOLVED
  CLOSED
}

enum SupportTicketPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum CampaignType {
  EMAIL
  SMS
  SOCIAL_MEDIA
  DIRECT_MAIL
  WEBINAR
  EVENT
}

enum CampaignStatus {
  DRAFT
  SCHEDULED
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

enum FeedbackStatus {
  SUBMITTED
  UNDER_REVIEW
  PLANNED
  IN_PROGRESS
  COMPLETED
  DECLINED
}

enum FeedbackType {
  BUG
  FEATURE
  IMPROVEMENT
  QUESTION
  OTHER
}

enum FeedbackPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum InboxType {
  EMAIL
  CHAT
  WHATSAPP
  SMS
  TWITTER
  FACEBOOK
  INSTAGRAM
  CUSTOM
}

enum ConversationStatus {
  OPEN
  RESOLVED
  PENDING
  SNOOZED
}

enum MessageStatus {
  PENDING
  RECEIVED
  SENT
  DELIVERED
  READ
  FAILED
}

enum ThreadStatus {
  ACTIVE
  PAUSED
  COMPLETED
  ARCHIVED
}

enum InteractionRole {
  user
  assistant
  system
}

enum CustomFieldType {
  TEXT
  TEXTAREA
  RICH_TEXT
  NUMBER
  CURRENCY
  PERCENT
  DATE
  DATETIME
  TIME
  BOOLEAN
  CHECKBOX
  SELECT
  MULTI_SELECT
  PICKLIST
  URL
  EMAIL
  PHONE
  FORMULA
  LOOKUP
  GEOLOCATION
  FILE
  IMAGE
}

enum OpportunityStage {
  DISCOVERY
  PROPOSAL_SENT
  NEGOTIATION
  CONTRACT_SENT
  CLOSED_WON
  CLOSED_LOST
}

enum OpportunityStatus {
  OPEN
  CLOSED_WON
  CLOSED_LOST
}

enum RelatedToType {
  LEAD
  OPPORTUNITY
  CONTACT
  ACCOUNT
  TASK
  TICKET
  QUOTE
}

enum FamilyRelationship {
  SPOUSE
  CHILD
  INFANT
  PARENT
  OTHER
}

enum Currency {
  USD
  CHF
  EUR
  GBP
  INR
  CAD
  AUD
  JPY
  SGD
  BRL
  CNY
  KRW
  MXN
  RUB
  ZAR
  SEK
  NOK
  DKK
  PLN
  CZK
  HUF
  RON
  BGN
  HRK
  TRY
  THB
  MYR
  IDR
  PHP
  VND
  AED
  SAR
  QAR
  KWD
  BHD
  OMR
  JOD
  LBP
  EGP
  MAD
  TND
  DZD
  NGN
  KES
  GHS
  UGX
  TZS
  ZMW
  BWP
  NAM
  ZWL
  MUR
  SCR
  KMF
  DJF
  ETB
  ERN
  SDG
  SSP
  SOS
  TJS
  UZS
  KGS
  TMT
  AFN
  PKR
  NPR
  BDT
  LKR
  MMK
  KHR
  LAK
  MNT
  KPW
  HKD
  TWD
  CLP
  PEN
  UYU
  ARS
  COP
  VEF
  BOB
  PYG
  GYD
  SRD
  FJD
  PGK
  WST
  TOP
  VUV
  SBD
  TVD
  KID
  XPF
  CFP
  NZD
}

enum OpportunityLineStatus {
  ACTIVE
  INACTIVE
  PENDING
}

enum TaskStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

enum LeadPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum OpportunityPriority {
  LOW
  MEDIUM
  HIGH
  URGENT
}

enum QuoteStatus {
  DRAFT
  IN_REVIEW
  SENT
  ACCEPTED
  REJECTED
  EXPIRED
  CONVERTED
  CANCELLED
}

enum TicketType {
  ISSUE
  REQUEST
  BUG
  FEEDBACK
  QUESTION
  COMPLAINT
  FEATURE
  OTHER
}

enum TicketChannel {
  EMAIL
  WHATSAPP
  WEBCHAT
  PHONE
  SMS
  TWITTER
  FACEBOOK
  INSTAGRAM
  API
  MANUAL
  OTHER
}

enum TicketCreationSource {
  CONVERSATION
  MANUAL
  API
  IMPORTED
  SYSTEM
}

enum TicketResolutionType {
  AI
  AGENT
  MIXED
  MISSED
  ABANDONED
  REOPENED
  ESCALATED
}

enum ActionType {
  CREATE
  UPDATE
  VIEW
  DELETE
}

enum ActivityVisibility {
  PRIVATE
  INTERNAL
  TEAM
}

enum ApprovalStatus {
  PENDING
  APPROVED
  REJECTED
  CANCELLED
}

enum RoadmapStatus {
  PLANNED
  IN_PROGRESS
  COMPLETED
  ON_HOLD
  CANCELLED
}

enum RoadmapPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum ProductType {
  GOODS
  SERVICE
}

enum TaxPreference {
  TAXABLE
  NON_TAXABLE
  EXEMPT
}

enum PackageStatus {
  ACTIVE
  INACTIVE
  DRAFT
  ARCHIVED
}

enum EmailProcessingStatus {
  PENDING
  PROCESSING
  SUCCESS
  FAILED
  SPAM_DETECTED
  DUPLICATE
  INVALID_TENANT
}

enum WebhookDeliveryStatus {
  PENDING
  SUCCESS
  FAILED
  RETRYING
  CANCELLED
}

enum DiscountType {
  PERCENTAGE
  FLAT
}



enum CurrencyPosition {
  BEFORE
  AFTER
  BEFORE_WITH_SPACE
  AFTER_WITH_SPACE
}

enum DecimalSeparator {
  DOT
  COMMA
}

enum ThousandsSeparator {
  COMMA
  DOT
  SPACE
  APOSTROPHE
  NONE
}

enum OpportunityType {
  NEW_BUSINESS
  EXISTING_BUSINESS
  RENEWAL
  UPSELL
}

enum PriceBookStatus {
  ACTIVE
  INACTIVE
  DRAFT
  ARCHIVED
}

enum HelpContentStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  UNDER_REVIEW
}

enum VoteType {
  HELPFUL
  NOT_HELPFUL
}

enum WhatsAppMessageType {
  TEXT
  IMAGE
  AUDIO
  VIDEO
  DOCUMENT
  LOCATION
  CONTACT
  INTERACTIVE
  BUTTON
  LIST
  TEMPLATE
  STICKER
  REACTION
  SYSTEM
  UNKNOWN
}

enum WhatsAppMessageStatus {
  PENDING
  RECEIVED
  SENT
  DELIVERED
  READ
  FAILED
  DELETED
}

enum WhatsAppProcessingStatus {
  PENDING
  PROCESSING
  SUCCESS
  FAILED
  SKIPPED
  DUPLICATE
}

enum CRMEmailProvider {
  OUTLOOK
  GMAIL
  FLINKK_MAIL
}

enum CRMCommunicationStatus {
  ACTIVE
  INACTIVE
  TESTING
  ERROR
}

enum EmailMessageType {
  SENT
  RECEIVED
  DRAFT
}

enum EmailMessageStatus {
  PENDING
  SENT
  DELIVERED
  READ
  FAILED
  DRAFT
  SCHEDULED
}

enum EmailPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}

// ==========================================
// CRM Settings Models
// ==========================================

model LeadConversionMapping {
  id          String  @id @default(auto()) @map("_id") @db.ObjectId
  name        String  @default("Default Mapping") // Configuration name
  description String? // Optional description

  // Target entities configuration
  enabledEntities Json // ["Contact", "Account", "Deal"] - which entities to convert to

  // Field mappings configuration
  mappings Json // Detailed mapping configuration

  // System fields
  isActive  Boolean @default(true)
  isDefault Boolean @default(false) // Only one default per tenant
  version   Int     @default(1) // For versioning support

  // Audit fields
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  tenant    Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy User?  @relation("LeadConversionMappingCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy User?  @relation("LeadConversionMappingUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)

  @@unique([tenantId, name])
  @@index([tenantId, isActive])
  @@index([tenantId, isDefault])
}

// ==========================================
// Domain Management Models
// ==========================================

model Domain {
  id       String  @id @default(auto()) @map("_id") @db.ObjectId
  domain   String // e.g., "go.company.com"
  slug     String? // subdomain part (e.g., "go")
  verified Boolean @default(false)
  primary  Boolean @default(false) // Primary domain for tenant

  // Verification
  verificationRecord String? // TXT record value for verification
  verifiedAt         DateTime?

  // DNS Configuration
  dnsConfigured Boolean  @default(false)
  lastChecked   DateTime @default(now())

  // Redirect Configuration
  redirectUrl String? // Root domain redirect URL

  // Tenant Relationship
  tenantId String @db.ObjectId
  tenant   Tenant @relation(fields: [tenantId], references: [id], onDelete: Cascade)

  // Metadata
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([domain])
  @@index([tenantId])
  @@index([verified])
}

// ==========================================
// Currency Configuration Models
// ==========================================

model CurrencyConfiguration {
  id          String @id @default(auto()) @map("_id") @db.ObjectId
  currencyCode Currency // ISO 4217 currency code
  currencyName String // Full currency name (e.g., "US Dollar", "British Pound")
  currencySymbol String // Currency symbol (e.g., "$", "£", "₹")
  
  // Formatting Configuration
  decimalPlaces      Int                @default(2) // Number of decimal places
  decimalSeparator   DecimalSeparator   @default(DOT) // Decimal separator
  thousandsSeparator ThousandsSeparator @default(COMMA) // Thousands separator
  currencyPosition   CurrencyPosition   @default(BEFORE) // Position of currency symbol
  
  // Display Options
  showSymbol         Boolean @default(true) // Whether to show currency symbol
  showCode           Boolean @default(false) // Whether to show currency code
  useSymbolOnly      Boolean @default(true) // Whether to use symbol only or code
  
  // Conversion Settings (for multi-currency support)
  isBaseCurrency     Boolean @default(false) // Whether this is the base currency for the tenant
  exchangeRate       Float? // Exchange rate relative to base currency (if not base)
  lastUpdatedRate    DateTime? // When the exchange rate was last updated
  
  // Status and metadata
  isActive  Boolean @default(true)
  isDefault Boolean @default(false) // Whether this is the default currency for the tenant
  
  // Audit fields
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  tenant        Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy     User?         @relation("CurrencyConfigCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?         @relation("CurrencyConfigUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  
  @@unique([tenantId, currencyCode])
  @@index([tenantId])
  @@index([currencyCode])
  @@index([isDefault])
  @@index([isBaseCurrency])
}

// ==========================================
// Country Configuration Models
// ==========================================

model CountryConfiguration {
  id          String @id @default(auto()) @map("_id") @db.ObjectId
  countryCode String // ISO 3166-1 alpha-2 country code (e.g., "US", "GB", "IN")
  countryName String // Full country name (e.g., "United States", "United Kingdom", "India")
  
  // Regional Settings
  timezone        String? // Default timezone for the country
  dateFormat      String? // Default date format (e.g., "MM/DD/YYYY", "DD/MM/YYYY")
  timeFormat      String? // Default time format (12/24 hour)
  language        String? // Default language code (e.g., "en", "es", "fr")
  
  // Currency Configuration
  defaultCurrency Currency? // Default currency for this country
  currencySymbol  String? // Currency symbol for this country
  
  // Address Format
  addressFormat Json? // JSON structure defining address format for this country
  
  // Tax Configuration
  taxRegistrationRequired Boolean @default(false) // Whether tax registration is required
  vatNumberFormat        String? // VAT number format pattern
  taxNumberFormat        String? // Tax number format pattern
  
  // Business Rules
  businessDays Json? // Array of business days (0=Sunday, 1=Monday, etc.)
  holidays     Json? // Array of holiday dates for this country
  
  // Status and metadata
  isActive  Boolean @default(true)
  isDefault Boolean @default(false) // Whether this is the default country for the tenant
  
  // Audit fields
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  tenant        Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy     User?         @relation("CountryConfigCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?         @relation("CountryConfigUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  
  @@unique([tenantId, countryCode])
  @@index([tenantId])
  @@index([countryCode])
  @@index([isDefault])
}

// ==========================================
// Tax Category Models
// ==========================================

model TaxCategory {
  id          String @id @default(auto()) @map("_id") @db.ObjectId
  name        String // Tax category name (e.g., "Standard Rate", "Reduced Rate", "Zero Rate")
  description String? // Description of the tax category
  
  // Tax Rate Configuration
  taxRate     Float  @default(0) // Tax rate as percentage (e.g., 20.0 for 20%)
  isActive    Boolean @default(true)
  
  // Tax Type and Classification
  taxType     String? // Type of tax (e.g., "VAT", "GST", "Sales Tax")
  categoryCode String? // Category code for tax reporting
  
  // Regional Configuration
  countryCode String? // Country code this tax category applies to (if country-specific)
  stateCode   String? // State/province code (if state-specific)
  
  // Business Rules
  appliesToProducts    Boolean @default(true) // Whether this tax applies to products
  appliesToServices    Boolean @default(true) // Whether this tax applies to services
  appliesToShipping    Boolean @default(false) // Whether this tax applies to shipping
  appliesToDiscounts   Boolean @default(false) // Whether this tax applies to discounts
  
  // Exemption Rules
  exemptionThreshold Float? // Amount above which tax applies
  exemptionItems     Json? // JSON array of exempt item types
  
  // Status and metadata
  isDefault Boolean @default(false) // Whether this is the default tax category
  
  // Audit fields
  tenantId    String   @db.ObjectId
  createdById String?  @db.ObjectId
  updatedById String?  @db.ObjectId
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  tenant        Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  createdBy     User?         @relation("TaxCategoryCreatedBy", fields: [createdById], references: [id], onDelete: SetNull)
  updatedBy     User?         @relation("TaxCategoryUpdatedBy", fields: [updatedById], references: [id], onDelete: SetNull)
  
  // Reverse relations
  products Product[]
  packages Package[]
  
  @@unique([tenantId, name])
  @@index([tenantId])
  @@index([countryCode])
  @@index([isDefault])
  @@index([isActive])
}
