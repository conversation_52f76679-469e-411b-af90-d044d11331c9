/**
 * Enhanced formatting utilities for country-specific PDF generation
 */

import { toMajorUnits } from "@flinkk/money-math";

export interface CurrencyConfig {
  currencyCode: string;
  currencySymbol: string;
  decimalPlaces: number;
  decimalSeparator: "DOT" | "COMMA";
  thousandsSeparator: "COMMA" | "DOT" | "SPACE" | "APOSTROPHE" | "NONE";
  currencyPosition:
    | "BEFORE"
    | "AFTER"
    | "BEFORE_WITH_SPACE"
    | "AFTER_WITH_SPACE";
}

export interface TaxConfig {
  primaryTaxType: string;
  primaryTaxRate: number;
  primaryTaxName: string;
  secondaryTaxType?: string;
  secondaryTaxRate?: number;
  secondaryTaxName?: string;
  taxInclusivePricing: boolean;
  compoundTax: boolean;
  hasRegionalTax: boolean;
  regionalTaxRates?: any;
  taxNumberFormat?: string;
  taxNumberLabel?: string;
}

export interface CountryConfiguration {
  countryCode: string;
  countryName: string;
  currency: CurrencyConfig;
  tax: TaxConfig;
}

/**
 * Format currency amount from minor units according to country-specific rules for PDF
 */
export function formatCurrencyFromMinorUnitsForPDF(
  minorAmount: number,
  config: CurrencyConfig,
  options: {
    showSymbol?: boolean;
    showCode?: boolean;
    useSymbolOnly?: boolean;
  } = {},
): string {
  const { showSymbol = true, showCode = false, useSymbolOnly = true } = options;

  // Convert from minor units to major units
  const majorAmount = parseFloat(
    toMajorUnits(minorAmount, config.currencyCode),
  );

  // Handle decimal places
  const roundedAmount = Number(majorAmount.toFixed(config.decimalPlaces));

  // Split into integer and decimal parts
  const parts = roundedAmount.toString().split(".");
  let integerPart = parts[0];
  const decimalPart = parts[1] || "";

  // Apply thousands separator
  if (config.thousandsSeparator !== "NONE" && integerPart.length > 3) {
    const separator = getThousandsSeparator(config.thousandsSeparator);
    integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
  }

  // Combine integer and decimal parts
  let formattedNumber = integerPart;
  if (config.decimalPlaces > 0) {
    const decimalSep = getDecimalSeparator(config.decimalSeparator);
    const paddedDecimal = decimalPart.padEnd(config.decimalPlaces, "0");
    formattedNumber += decimalSep + paddedDecimal;
  }

  // Add currency symbol or code
  let currencyIndicator = "";
  if (showSymbol && useSymbolOnly) {
    currencyIndicator = config.currencySymbol;
  } else if (showCode) {
    currencyIndicator = config.currencyCode;
  } else if (showSymbol) {
    currencyIndicator = config.currencySymbol;
  }

  // Position currency indicator
  if (!currencyIndicator) {
    return formattedNumber;
  }

  switch (config.currencyPosition) {
    case "BEFORE":
      return `${currencyIndicator}${formattedNumber}`;
    case "AFTER":
      return `${formattedNumber}${currencyIndicator}`;
    case "BEFORE_WITH_SPACE":
      return `${currencyIndicator} ${formattedNumber}`;
    case "AFTER_WITH_SPACE":
      return `${formattedNumber} ${currencyIndicator}`;
    default:
      return `${currencyIndicator}${formattedNumber}`;
  }
}

/**
 * Format currency amount according to country-specific rules for PDF
 */
export function formatCurrencyForPDF(
  amount: number,
  config: CurrencyConfig,
  options: {
    showSymbol?: boolean;
    showCode?: boolean;
    useSymbolOnly?: boolean;
  } = {},
): string {
  const { showSymbol = true, showCode = false, useSymbolOnly = true } = options;

  // Handle decimal places
  const roundedAmount = Number(amount.toFixed(config.decimalPlaces));

  // Split into integer and decimal parts
  const parts = roundedAmount.toString().split(".");
  let integerPart = parts[0];
  const decimalPart = parts[1] || "";

  // Apply thousands separator
  if (config.thousandsSeparator !== "NONE" && integerPart.length > 3) {
    const separator = getThousandsSeparator(config.thousandsSeparator);
    integerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, separator);
  }

  // Combine integer and decimal parts
  let formattedNumber = integerPart;
  if (config.decimalPlaces > 0) {
    const decimalSep = getDecimalSeparator(config.decimalSeparator);
    const paddedDecimal = decimalPart.padEnd(config.decimalPlaces, "0");
    formattedNumber += decimalSep + paddedDecimal;
  }

  // Add currency symbol or code
  let currencyIndicator = "";
  if (showSymbol && useSymbolOnly) {
    currencyIndicator = config.currencySymbol;
  } else if (showCode) {
    currencyIndicator = config.currencyCode;
  } else if (showSymbol) {
    currencyIndicator = config.currencySymbol;
  }

  // Position currency indicator
  if (!currencyIndicator) {
    return formattedNumber;
  }

  switch (config.currencyPosition) {
    case "BEFORE":
      return `${currencyIndicator}${formattedNumber}`;
    case "AFTER":
      return `${formattedNumber}${currencyIndicator}`;
    case "BEFORE_WITH_SPACE":
      return `${currencyIndicator} ${formattedNumber}`;
    case "AFTER_WITH_SPACE":
      return `${formattedNumber} ${currencyIndicator}`;
    default:
      return `${currencyIndicator}${formattedNumber}`;
  }
}

/**
 * Calculate tax breakdown for PDF display
 */
export function calculateTaxForPDF(
  amount: number,
  taxConfig: TaxConfig,
  itemTaxRate?: number,
): {
  baseAmount: number;
  primaryTaxAmount: number;
  secondaryTaxAmount: number;
  totalTaxAmount: number;
  totalAmount: number;
  primaryTaxRate: number;
  secondaryTaxRate?: number;
  primaryTaxName: string;
  secondaryTaxName?: string;
} {
  const effectiveTaxRate = itemTaxRate ?? taxConfig.primaryTaxRate;
  let baseAmount = amount;
  let primaryTaxAmount = 0;
  let secondaryTaxAmount = 0;

  if (taxConfig.taxInclusivePricing) {
    // Amount includes tax - extract tax from total
    if (effectiveTaxRate > 0) {
      primaryTaxAmount = (amount * effectiveTaxRate) / (100 + effectiveTaxRate);
      baseAmount = amount - primaryTaxAmount;
    }

    if (taxConfig.secondaryTaxRate && taxConfig.secondaryTaxRate > 0) {
      if (taxConfig.compoundTax) {
        // Secondary tax is included in the total amount
        const totalTaxRate =
          effectiveTaxRate +
          taxConfig.secondaryTaxRate +
          (effectiveTaxRate * taxConfig.secondaryTaxRate) / 100;
        const totalTaxAmount = (amount * totalTaxRate) / (100 + totalTaxRate);
        secondaryTaxAmount = totalTaxAmount - primaryTaxAmount;
        baseAmount = amount - totalTaxAmount;
      } else {
        // Secondary tax calculated on base amount
        secondaryTaxAmount = (baseAmount * taxConfig.secondaryTaxRate) / 100;
      }
    }
  } else {
    // Amount excludes tax - add tax to base
    baseAmount = amount;

    if (effectiveTaxRate > 0) {
      primaryTaxAmount = (baseAmount * effectiveTaxRate) / 100;
    }

    if (taxConfig.secondaryTaxRate && taxConfig.secondaryTaxRate > 0) {
      if (taxConfig.compoundTax) {
        // Secondary tax calculated on amount including primary tax
        secondaryTaxAmount =
          ((baseAmount + primaryTaxAmount) * taxConfig.secondaryTaxRate) / 100;
      } else {
        // Secondary tax calculated on base amount
        secondaryTaxAmount = (baseAmount * taxConfig.secondaryTaxRate) / 100;
      }
    }
  }

  const totalTaxAmount = primaryTaxAmount + secondaryTaxAmount;
  const totalAmount = taxConfig.taxInclusivePricing
    ? amount
    : baseAmount + totalTaxAmount;

  return {
    baseAmount: Number(baseAmount.toFixed(2)),
    primaryTaxAmount: Number(primaryTaxAmount.toFixed(2)),
    secondaryTaxAmount: Number(secondaryTaxAmount.toFixed(2)),
    totalTaxAmount: Number(totalTaxAmount.toFixed(2)),
    totalAmount: Number(totalAmount.toFixed(2)),
    primaryTaxRate: effectiveTaxRate,
    secondaryTaxRate: taxConfig.secondaryTaxRate,
    primaryTaxName: taxConfig.primaryTaxName,
    secondaryTaxName: taxConfig.secondaryTaxName,
  };
}

/**
 * Format tax number according to country-specific format
 */
export function formatTaxNumber(
  taxNumber: string,
  taxConfig: TaxConfig,
): string {
  if (!taxNumber || !taxConfig.taxNumberFormat) {
    return taxNumber;
  }

  // Simple formatting - can be enhanced with more sophisticated patterns
  return taxNumber;
}

/**
 * Get tax display label for country
 */
export function getTaxDisplayLabel(taxConfig: TaxConfig): string {
  return taxConfig.taxNumberLabel || "Tax Number";
}

/**
 * Format date according to country preferences
 */
export function formatDateForCountry(
  date: string | Date,
  countryCode: string,
): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;

  // Country-specific date formatting
  const localeMap: Record<string, string> = {
    US: "en-US",
    GB: "en-GB",
    IN: "en-IN",
    CA: "en-CA",
    AU: "en-AU",
    DE: "de-DE",
    FR: "fr-FR",
    JP: "ja-JP",
    SG: "en-SG",
    BR: "pt-BR",
  };

  const locale = localeMap[countryCode] || "en-US";

  return dateObj.toLocaleDateString(locale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

/**
 * Get appropriate thousands separator character
 */
function getThousandsSeparator(
  separator: CurrencyConfig["thousandsSeparator"],
): string {
  switch (separator) {
    case "COMMA":
      return ",";
    case "DOT":
      return ".";
    case "SPACE":
      return " ";
    case "APOSTROPHE":
      return "'";
    case "NONE":
    default:
      return "";
  }
}

/**
 * Get appropriate decimal separator character
 */
function getDecimalSeparator(
  separator: CurrencyConfig["decimalSeparator"],
): string {
  switch (separator) {
    case "COMMA":
      return ",";
    case "DOT":
    default:
      return ".";
  }
}

/**
 * Create default currency configuration for fallback
 */
export function createDefaultCurrencyConfig(): CurrencyConfig {
  return {
    currencyCode: "USD",
    currencySymbol: "$",
    decimalPlaces: 2,
    decimalSeparator: "DOT",
    thousandsSeparator: "COMMA",
    currencyPosition: "BEFORE",
  };
}

/**
 * Create default tax configuration for fallback
 */
export function createDefaultTaxConfig(): TaxConfig {
  return {
    primaryTaxType: "SALES_TAX",
    primaryTaxRate: 0,
    primaryTaxName: "Sales Tax",
    taxInclusivePricing: false,
    compoundTax: false,
    hasRegionalTax: false,
    taxNumberLabel: "Tax Number",
  };
}

/**
 * Transform country configuration for PDF use
 */
export function transformCountryConfigForPDF(
  config: any,
): CountryConfiguration {
  return {
    countryCode: config.countryCode,
    countryName: config.countryName,
    currency: {
      currencyCode: config.currencyCode,
      currencySymbol: config.currencySymbol,
      decimalPlaces: config.decimalPlaces,
      decimalSeparator: config.decimalSeparator,
      thousandsSeparator: config.thousandsSeparator,
      currencyPosition: config.currencyPosition,
    },
    tax: {
      primaryTaxType: config.primaryTaxType,
      primaryTaxRate: config.primaryTaxRate,
      primaryTaxName: config.primaryTaxName,
      secondaryTaxType: config.secondaryTaxType,
      secondaryTaxRate: config.secondaryTaxRate,
      secondaryTaxName: config.secondaryTaxName,
      taxInclusivePricing: config.taxInclusivePricing,
      compoundTax: config.compoundTax,
      hasRegionalTax: config.hasRegionalTax,
      regionalTaxRates: config.regionalTaxRates,
      taxNumberFormat: config.taxNumberFormat,
      taxNumberLabel: config.taxNumberLabel,
    },
  };
}
