import { QuotePayload, QuoteData, LineItem, PackageLineItem } from "../types";

/**
 * Calculate line total for a line item with discounts and tax
 */
export function calculateLineTotal(item: LineItem): number {
  // Use manual line total if manual pricing is enabled
  if (item.is_manual_pricing && item.manual_line_total !== undefined) {
    return item.manual_line_total;
  }

  const subtotal = item.quantity * item.unit_price;

  // Calculate discount
  let discountAmount = 0;
  if (item.discount_value && item.discount_value > 0) {
    if (item.discount_type === "PERCENTAGE") {
      discountAmount = (subtotal * item.discount_value) / 100;
    } else {
      discountAmount = item.discount_value;
    }
  }

  // Calculate tax on amount after discount
  const amountAfterDiscount = subtotal - discountAmount;
  const taxAmount = item.tax_rate
    ? (amountAfterDiscount * item.tax_rate) / 100
    : 0;

  return amountAfterDiscount + taxAmount;
}

/**
 * Calculate line total for a package item with discounts and tax
 */
export function calculatePackageLineTotal(item: PackageLineItem): number {
  const subtotal = item.quantity * item.unit_price;

  // Calculate discount
  let discountAmount = 0;
  if (item.discount_value && item.discount_value > 0) {
    if (item.discount_type === "PERCENTAGE") {
      discountAmount = (subtotal * item.discount_value) / 100;
    } else {
      discountAmount = item.discount_value;
    }
  }

  // Calculate tax on amount after discount
  const amountAfterDiscount = subtotal - discountAmount;
  const taxAmount = item.tax_rate
    ? (amountAfterDiscount * item.tax_rate) / 100
    : 0;

  return amountAfterDiscount + taxAmount;
}

/**
 * Calculate discount amount for a line item
 */
export function calculateLineDiscount(item: LineItem): number {
  const subtotal = item.quantity * item.unit_price;

  if (!item.discount_value || item.discount_value <= 0) {
    return 0;
  }

  if (item.discount_type === "PERCENTAGE") {
    return (subtotal * item.discount_value) / 100;
  } else {
    return item.discount_value;
  }
}

/**
 * Calculate tax amount for a line item
 */
export function calculateLineTax(item: LineItem): number {
  const subtotal = item.quantity * item.unit_price;
  const discountAmount = calculateLineDiscount(item);
  const amountAfterDiscount = subtotal - discountAmount;

  return item.tax_rate ? (amountAfterDiscount * item.tax_rate) / 100 : 0;
}

/**
 * Calculate discount amount for a package item
 */
export function calculatePackageDiscount(item: PackageLineItem): number {
  const subtotal = item.quantity * item.unit_price;

  if (!item.discount_value || item.discount_value <= 0) {
    return 0;
  }

  if (item.discount_type === "PERCENTAGE") {
    return (subtotal * item.discount_value) / 100;
  } else {
    return item.discount_value;
  }
}

/**
 * Calculate tax amount for a package item
 */
export function calculatePackageTax(item: PackageLineItem): number {
  const subtotal = item.quantity * item.unit_price;
  const discountAmount = calculatePackageDiscount(item);
  const amountAfterDiscount = subtotal - discountAmount;

  return item.tax_rate ? (amountAfterDiscount * item.tax_rate) / 100 : 0;
}

/**
 * Calculate subtotal for all line items
 */
export function calculateSubtotal(lineItems: LineItem[]): number {
  return lineItems.reduce((sum, item) => sum + calculateLineTotal(item), 0);
}

/**
 * Calculate subtotal for all package items
 */
export function calculatePackageSubtotal(
  packageItems: PackageLineItem[],
): number {
  return packageItems.reduce(
    (sum, item) => sum + calculatePackageLineTotal(item),
    0,
  );
}

/**
 * Calculate quote-level discount
 */
export function calculateQuoteDiscount(
  subtotal: number,
  discountType: string,
  discountValue: number,
): number {
  if (!discountValue || discountValue <= 0) {
    return 0;
  }

  if (discountType === "PERCENTAGE") {
    return (subtotal * discountValue) / 100;
  } else {
    return discountValue;
  }
}

/**
 * Calculate total tax for quote
 */
export function calculateQuoteTax(
  amountAfterDiscount: number,
  intraStateTaxRate: number,
  interStateTaxRate: number,
): number {
  const intraStateTax = (amountAfterDiscount * intraStateTaxRate) / 100;
  const interStateTax = (amountAfterDiscount * interStateTaxRate) / 100;
  return intraStateTax + interStateTax;
}

/**
 * Calculate grand total with enhanced pricing
 */
export function calculateGrandTotal(
  subtotal: number,
  quoteDiscountType: string = "PERCENTAGE",
  quoteDiscountValue: number = 0,
  intraStateTaxRate: number = 0,
  interStateTaxRate: number = 0,
  adjustments: number = 0,
  isManualTotal: boolean = false,
  manualTotalValue?: number,
  isManualGrandTotal: boolean = false,
  manualGrandTotal?: number,
): number {
  // Manual grand total takes precedence over manual total
  if (isManualGrandTotal && manualGrandTotal !== undefined) {
    return manualGrandTotal;
  }

  if (isManualTotal && manualTotalValue !== undefined) {
    return manualTotalValue;
  }

  const quoteDiscount = calculateQuoteDiscount(
    subtotal,
    quoteDiscountType,
    quoteDiscountValue,
  );
  const amountAfterDiscount = subtotal - quoteDiscount;
  const totalTax = calculateQuoteTax(
    amountAfterDiscount,
    intraStateTaxRate,
    interStateTaxRate,
  );

  return amountAfterDiscount + totalTax + adjustments;
}

/**
 * Transform quote payload into quote data with calculated fields
 */
export function transformQuoteData(payload: QuotePayload): QuoteData {
  // Ensure line_items and package_items are always arrays
  const line_items = payload.line_items || [];
  const package_items = payload.package_items || [];

  const line_items_with_totals = line_items.map((item) => ({
    ...item,
    line_total: calculateLineTotal(item),
    discount_amount: calculateLineDiscount(item),
    tax_amount: calculateLineTax(item),
  }));

  const package_items_with_totals = package_items.map((item) => ({
    ...item,
    line_total: calculatePackageLineTotal(item),
    discount_amount: calculatePackageDiscount(item),
    tax_amount: calculatePackageTax(item),
  }));

  const products_subtotal = calculateSubtotal(line_items);
  const packages_subtotal = calculatePackageSubtotal(package_items);
  const subtotal = products_subtotal + packages_subtotal;

  const quote_discount = calculateQuoteDiscount(
    subtotal,
    payload.quote_discount_type || "PERCENTAGE",
    payload.quote_discount_value || 0,
  );
  const amountAfterDiscount = subtotal - quote_discount;
  const total_tax = calculateQuoteTax(
    amountAfterDiscount,
    payload.intra_state_tax_rate || 0,
    payload.inter_state_tax_rate || 0,
  );
  const grand_total = calculateGrandTotal(
    subtotal,
    payload.quote_discount_type,
    payload.quote_discount_value,
    payload.intra_state_tax_rate,
    payload.inter_state_tax_rate,
    payload.adjustments,
    payload.is_manual_total,
    payload.manual_total_value,
    payload.is_manual_grand_total,
    payload.manual_grand_total,
  );

  return {
    ...payload,
    line_items, // Use the safe arrays
    package_items, // Use the safe arrays
    subtotal,
    products_subtotal,
    packages_subtotal,
    quote_discount,
    total_tax,
    grand_total,
    line_items_with_totals,
    package_items_with_totals,
  };
}

import {
  formatCurrency as formatCurrencyMath,
  toMinorUnits,
} from "@flinkk/money-math";

/**
 * Format currency for display using @flinkk/money-math
 */
export function formatCurrency(
  amount: number,
  currency: string = "USD",
): string {
  const minorUnits = toMinorUnits(amount, currency);
  return formatCurrencyMath(minorUnits, currency);
}

/**
 * Format date for display
 */
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleDateString("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
}

/**
 * Generate filename for quote PDF
 */
export function generateQuoteFilename(quoteNumber: string): string {
  return `quote-${quoteNumber}.pdf`;
}

/**
 * Validate quote data before PDF generation
 */
export function validateQuoteData(data: any): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (!data.quote_number?.trim()) {
    errors.push("Quote number is required");
  }

  if (!data.customer_name?.trim()) {
    errors.push("Customer name is required");
  }

  if (!data.customer_address?.trim()) {
    errors.push("Customer address is required");
  }

  // Check if at least one product or package exists
  const hasLineItems =
    data.line_items &&
    Array.isArray(data.line_items) &&
    data.line_items.length > 0;
  const hasPackageItems =
    data.package_items &&
    Array.isArray(data.package_items) &&
    data.package_items.length > 0;

  if (!hasLineItems && !hasPackageItems) {
    errors.push("At least one product or package item is required");
  }

  // Validate line items if they exist
  if (hasLineItems) {
    data.line_items.forEach((item: any, index: number) => {
      if (!item.description?.trim()) {
        errors.push(`Product ${index + 1}: Description is required`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Product ${index + 1}: Quantity must be greater than 0`);
      }
      if (!item.unit_price || item.unit_price <= 0) {
        errors.push(`Product ${index + 1}: Unit price must be greater than 0`);
      }
    });
  }

  // Validate package items if they exist
  if (hasPackageItems) {
    data.package_items.forEach((item: any, index: number) => {
      if (!item.package_name?.trim()) {
        errors.push(`Package ${index + 1}: Package name is required`);
      }
      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Package ${index + 1}: Quantity must be greater than 0`);
      }
      if (!item.unit_price || item.unit_price <= 0) {
        errors.push(`Package ${index + 1}: Unit price must be greater than 0`);
      }
    });
  }

  if (data.date_issued && data.valid_until) {
    const issued = new Date(data.date_issued);
    const valid = new Date(data.valid_until);
    if (valid < issued) {
      errors.push("Valid until date must be on or after issue date");
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}
