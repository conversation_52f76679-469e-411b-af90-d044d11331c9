"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Form } from "@flinkk/components/ui/form";
import { FormElements } from "@flinkk/dynamic-form/form-elements";
import { SectionWrapper } from "@flinkk/dynamic-form/components";
import { Loader2, Save } from "lucide-react";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { LogoUpload } from "@flinkk/shared-components/logo-upload";
import { useRouter } from "next/navigation";
import { useQueryClient } from "@tanstack/react-query";

// Form validation schema
const organizationFormSchema = z.object({
  name: z.string().min(1, "Organization name is required"),
  domain: z.string().optional(),
  timezone: z.string().min(1, "Timezone is required"),

  // Business Details
  businessName: z.string().optional(),
  gstin: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  postalCode: z.string().optional(),
  state: z.string().optional(),

  // Tax Information (now editable fields)
  currency: z.string().min(1, "Currency is required"),
  taxSystem: z.string().min(1, "Tax system is required"),
  taxInclusive: z.string().min(1, "Tax inclusive setting is required"),
});

interface Organization {
  id: string;
  name: string;
  domain?: string;
  logo?: string;
  timezone?: string;
  businessName?: string;
  gstin?: string;
  address?: string;
  city?: string;
  postalCode?: string;
  state?: string;
  currency?: string;
  taxSystem?: string;
  taxInclusive?: string;
}

type OrganizationFormValues = z.infer<typeof organizationFormSchema>;

interface GeneralSettingsClientProps {
  initialData: Organization;
  currencyConfiguration?: any;
}

// Available currencies with their default tax settings
const availableCurrencies = [
  {
    code: "USD",
    name: "USD",
    symbol: "$",
    taxSystem: "Sales Tax (0%)",
    taxInclusive: "No",
  },
  {
    code: "EUR",
    name: "EUR",
    symbol: "€",
    taxSystem: "VAT (20%)",
    taxInclusive: "Yes",
  },
  {
    code: "GBP",
    name: "GBP",
    symbol: "£",
    taxSystem: "VAT (20%)",
    taxInclusive: "Yes",
  },
  {
    code: "INR",
    name: "INR",
    symbol: "₹",
    taxSystem: "GST (18%)",
    taxInclusive: "Yes",
  },
  {
    code: "CAD",
    name: "CAD",
    symbol: "$",
    taxSystem: "GST (5%)",
    taxInclusive: "No",
  },
  {
    code: "AUD",
    name: "AUD",
    symbol: "$",
    taxSystem: "GST (10%)",
    taxInclusive: "Yes",
  },
  {
    code: "JPY",
    name: "JPY",
    symbol: "¥",
    taxSystem: "Consumption Tax (10%)",
    taxInclusive: "Yes",
  },
  {
    code: "SGD",
    name: "SGD",
    symbol: "$",
    taxSystem: "GST (9%)",
    taxInclusive: "Yes",
  },
  {
    code: "BRL",
    name: "BRL",
    symbol: "R$",
    taxSystem: "ICMS (17%)",
    taxInclusive: "Yes",
  },
  {
    code: "CHF",
    name: "CHF",
    symbol: "CHF",
    taxSystem: "VAT (7.7%)",
    taxInclusive: "Yes",
  },
  {
    code: "CNY",
    name: "CNY",
    symbol: "¥",
    taxSystem: "VAT (13%)",
    taxInclusive: "Yes",
  },
  {
    code: "KRW",
    name: "KRW",
    symbol: "₩",
    taxSystem: "VAT (10%)",
    taxInclusive: "Yes",
  },
];

// Tax system options
const taxSystemOptions = [
  { label: "Sales Tax (0%)", value: "Sales Tax (0%)" },
  { label: "VAT (20%)", value: "VAT (20%)" },
  { label: "VAT (19%)", value: "VAT (19%)" },
  { label: "VAT (7.7%)", value: "VAT (7.7%)" },
  { label: "GST (18%)", value: "GST (18%)" },
  { label: "GST (5%)", value: "GST (5%)" },
  { label: "GST (10%)", value: "GST (10%)" },
  { label: "GST (9%)", value: "GST (9%)" },
  { label: "Consumption Tax (10%)", value: "Consumption Tax (10%)" },
  { label: "ICMS (17%)", value: "ICMS (17%)" },
  { label: "VAT (13%)", value: "VAT (13%)" },
  { label: "VAT (10%)", value: "VAT (10%)" },
  { label: "No Tax", value: "No Tax" },
];

// Tax inclusive options
const taxInclusiveOptions = [
  { label: "Yes", value: "Yes" },
  { label: "No", value: "No" },
];

export function PageClient({
  initialData,
  currencyConfiguration: initialCurrencyConfiguration,
}: GeneralSettingsClientProps) {
  const organization = initialData;
  const router = useRouter();
  const [currencyConfiguration] = useState<any>(initialCurrencyConfiguration);
  const queryClient = useQueryClient();

  // Form setup with React Hook Form
  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationFormSchema),
    defaultValues: {
      name: organization?.name || "",
      domain: organization?.domain || "",
      timezone: organization?.timezone || "UTC",
      businessName: organization?.businessName || "",
      gstin: organization?.gstin || "",
      address: organization?.address || "",
      city: organization?.city || "",
      postalCode: organization?.postalCode || "",
      state: organization?.state || "",
      currency: organization?.currency || "",
      taxSystem: organization?.taxSystem || "",
      taxInclusive: organization?.taxInclusive || "",
    },
  });

  // Auto-populate tax information when currency changes
  const handleCurrencyChange = (currencyCode: string) => {
    const selectedCurrency = availableCurrencies.find(
      (currency) => currency.code === currencyCode,
    );
    if (selectedCurrency) {
      form.setValue("taxSystem", selectedCurrency.taxSystem);
      form.setValue("taxInclusive", selectedCurrency.taxInclusive);
    }
  };

  // Watch for currency changes and auto-populate tax information
  const watchedCurrency = form.watch("currency");
  useEffect(() => {
    if (watchedCurrency) {
      handleCurrencyChange(watchedCurrency);
    }
  }, [watchedCurrency]);

  // Initialize tax information based on current currency
  useEffect(() => {
    if (organization?.currency) {
      const currentCurrency = availableCurrencies.find(
        (currency) => currency.code === organization.currency,
      );
      if (currentCurrency) {
        form.setValue("taxSystem", currentCurrency.taxSystem);
        form.setValue("taxInclusive", currentCurrency.taxInclusive);
      }
    }
  }, [organization?.currency, form]);

  // Use standardized hook for saving organization data
  const { save: saveOrganization, isLoading: isSaving } = useSaveFormData({
    model: "tenant", // Organization model is called "tenant" in the database
    onSuccess: () => {
      // Refresh the page to get updated currency configuration
      router.refresh();
    },
    successMessage: "Organization settings updated successfully",
  });

  // Handle form submission using useSaveFormData hook
  const onSubmit = async (data: OrganizationFormValues) => {
    if (!organization) {
      return;
    }

    console.log("Form data to save:", data);

    // Save organization data including currency settings
    await saveOrganization(organization.id, data);

    // If currency changed, update the currency configuration
    if (data.currency && data.currency !== organization.currency) {
      try {
        console.log("Currency changed to:", data.currency);

        // Get the selected currency details
        const selectedCurrency = availableCurrencies.find(
          (c) => c.code === data.currency,
        );
        if (!selectedCurrency) {
          console.error("Selected currency not found in available currencies");
          toast.error("Selected currency not found");
          return;
        }

        // First, unset all existing defaults and base currencies
        await fetch("/api/currency-configurations/reset-defaults", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        });

        // Call the currency configuration API to create/update the configuration
        const response = await fetch("/api/currency-configurations", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            currencyCode: data.currency,
            currencyName: selectedCurrency.name,
            currencySymbol: selectedCurrency.symbol,
            decimalPlaces: 2,
            decimalSeparator: "DOT",
            thousandsSeparator: "COMMA",
            currencyPosition: "BEFORE",
            showSymbol: true,
            showCode: false,
            useSymbolOnly: true,
            isDefault: true,
            isBaseCurrency: true,
            isActive: true,
          }),
        });

        if (response.ok) {
          console.log("Currency configuration updated successfully");
          toast.success("Currency configuration updated successfully");

          // Invalidate currency cache to ensure fresh data
          queryClient.invalidateQueries({
            queryKey: ["currency-configuration", "default"],
          });
          queryClient.invalidateQueries({
            queryKey: ["currency-configurations"],
          });

          // Refresh the page to get updated currency configuration
          router.refresh();
        } else {
          console.error("Failed to update currency configuration");
          toast.error("Failed to update currency configuration");
        }
      } catch (error) {
        console.error("Error updating currency configuration:", error);
        toast.error("Error updating currency configuration");
      }
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Organization Information Section */}
        <SectionWrapper
          title="Organization Information"
          description="Basic details about your organization"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormElements
              control={form.control}
              name="name"
              label="Organization Name"
              type="text"
              placeholder="Enter organization name"
              required
              disabled={isSaving}
            />
            <FormElements
              control={form.control}
              name="domain"
              label="Website Domain"
              type="text"
              placeholder="example.com"
              disabled={isSaving}
              helperText="Enter your website domain without https://"
            />
          </div>
        </SectionWrapper>

        {/* Organization Logo Section */}
        <SectionWrapper
          title="Organization Logo"
          description="Upload your organization's logo for branding"
        >
          <LogoUpload
            currentLogo={organization?.logo}
            onUploadSuccess={(logoUrl) => {
              // Update the organization data
              if (organization) {
                organization.logo = logoUrl;
              }
              toast.success("Logo updated successfully");
            }}
            onUploadError={(error) => {
              toast.error(`Logo upload failed: ${error}`);
            }}
            onRemoveSuccess={() => {
              // Update the organization data
              if (organization) {
                organization.logo = undefined;
              }
              toast.success("Logo removed successfully");
            }}
            disabled={isSaving}
          />
        </SectionWrapper>

        {/* Regional Settings Section */}
        <SectionWrapper
          title="Regional Settings"
          description="Configure timezone and regional preferences"
        >
          <FormElements
            control={form.control}
            name="timezone"
            label="Timezone"
            type="select"
            placeholder="Select timezone"
            required
            disabled={isSaving}
            options={[
              { label: "UTC (Coordinated Universal Time)", value: "UTC" },
              { label: "Eastern Time (UTC-5/-4)", value: "America/New_York" },
              { label: "Central Time (UTC-6/-5)", value: "America/Chicago" },
              { label: "Mountain Time (UTC-7/-6)", value: "America/Denver" },
              {
                label: "Pacific Time (UTC-8/-7)",
                value: "America/Los_Angeles",
              },
              { label: "London (UTC+0/+1)", value: "Europe/London" },
              { label: "Paris (UTC+1/+2)", value: "Europe/Paris" },
              { label: "Tokyo (UTC+9)", value: "Asia/Tokyo" },
              { label: "Shanghai (UTC+8)", value: "Asia/Shanghai" },
              { label: "Sydney (UTC+10/+11)", value: "Australia/Sydney" },
            ]}
          />
        </SectionWrapper>

        {/* Business Details Section */}
        <SectionWrapper
          title="Business Details"
          description="Complete business information for quotations and documents"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <FormElements
              control={form.control}
              name="businessName"
              label="Business Name"
              type="text"
              placeholder="Enter business name"
              disabled={isSaving}
            />
            <FormElements
              control={form.control}
              name="gstin"
              label="Tax Number"
              type="text"
              placeholder="Enter tax number"
              disabled={isSaving}
            />
            <div></div>
            <FormElements
              control={form.control}
              name="city"
              label="City"
              type="text"
              placeholder="Enter city"
              disabled={isSaving}
            />
            <FormElements
              control={form.control}
              name="postalCode"
              label="Postal Code"
              type="text"
              placeholder="Enter postal code"
              disabled={isSaving}
            />
            <FormElements
              control={form.control}
              name="state"
              label="State"
              type="text"
              placeholder="Enter state"
              disabled={isSaving}
            />
          </div>

          <div className="mt-4">
            <FormElements
              control={form.control}
              name="address"
              label="Business Address"
              type="textarea"
              placeholder="Enter complete business address"
              disabled={isSaving}
              rows={3}
            />
          </div>
        </SectionWrapper>

        {/* Tax Information Section */}
        <SectionWrapper
          title="Tax Information"
          description="Configure tax settings and currency for your business"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormElements
              control={form.control}
              name="currency"
              label="Currency"
              type="select"
              placeholder="Choose currency"
              required
              disabled={isSaving}
              options={availableCurrencies.map((currency) => ({
                label: `${currency.name} (${currency.symbol})`,
                value: currency.code,
              }))}
              helperText="Select your primary currency for transactions"
            />
            <FormElements
              control={form.control}
              name="taxSystem"
              label="Tax System"
              type="select"
              placeholder="Choose tax system"
              required
              disabled={isSaving}
              options={taxSystemOptions}
              helperText="Select the tax system for your business"
            />
            <FormElements
              control={form.control}
              name="taxInclusive"
              label="Tax Inclusive"
              type="select"
              placeholder="Choose tax inclusive setting"
              required
              disabled={isSaving}
              options={taxInclusiveOptions}
              helperText="Whether prices include tax by default"
            />
          </div>
        </SectionWrapper>

        {/* Submit Button */}
        <div className="flex justify-end">
          <Button type="submit" disabled={isSaving}>
            {isSaving ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
