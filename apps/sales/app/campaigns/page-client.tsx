"use client";

import type { Campaign } from "@/types/campaign";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import * as React from "react";

import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableSortList } from "@flinkk/data-table/component/data-table-sort-list";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDelete } from "@flinkk/hooks";
import { getCampaignsTableColumns } from "./columns";
import { useRouter } from "next/navigation";

import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog
    ),
  {
    ssr: false,
  }
);

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface CampaignsTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
}

export function CampaignsTable({
  data,
  pageCount,
  permissions,
}: CampaignsTableProps) {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Campaign> | null>(null);

  const { deleteRecord } = useDelete({
    model: "campaign",
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      setRowAction(null);
      router.refresh();
    },
  });

  const handleDeleteCampaign = async () => {
    if (rowAction?.type === "delete" && rowAction.row) {
      await deleteRecord(rowAction.row.id);
    }
  };

  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setIsDeleteDialogOpen(true);
    } else if (rowAction?.type === "view" && rowAction.row) {
      // Navigate to the view page for the campaign with proper title
      const campaignName = rowAction.row.name || "Unnamed Campaign";
      router.push(
        `/campaigns/${rowAction.row.id}?title=${encodeURIComponent(campaignName)}&mode=view`,
      );
      setRowAction(null);
    } else if (rowAction?.type === "edit" && rowAction.row) {
      // Navigate to the same page but with edit mode, passing campaign name for title
      const campaignName = rowAction.row.name || "Unnamed Campaign";
      const url = `/campaigns/${rowAction.row.id}?title=${encodeURIComponent(`Edit - ${campaignName}`)}`;
      router.push(url);
    }
  }, [rowAction, router]);

  const columns = React.useMemo(
    () =>
      getCampaignsTableColumns({
        setRowAction,
        permissions,
      }),
    [setRowAction, permissions],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        onRowClick={(row) => {
          // Pass campaign name for title when viewing
          const campaignName = row.name || "Unnamed Campaign";
          const url = `/campaigns/${row.id}?title=${encodeURIComponent(campaignName)}`;
          router.push(url);
        }}
        doctype="campaign"
      >
        <DataTableToolbar
          table={table}
          buttonText={permissions.canCreate ? "New Campaign" : undefined}
          href={permissions.canCreate ? "/campaigns/new" : undefined}
        >
          <DataTableSortList table={table} align="end" />
        </DataTableToolbar>
      </DataTable>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && permissions.canDelete && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onDelete={handleDeleteCampaign}
          isLoading={isDeleting}
          title="Delete Campaign"
          description="Are you sure you want to delete this campaign? This action cannot be undone."
        />
      )}
    </>
  );
}
