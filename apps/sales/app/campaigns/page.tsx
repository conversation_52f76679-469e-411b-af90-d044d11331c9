import * as React from "react";
import { CampaignsTable } from "./page-client";
import { getCampaigns } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";
import { NoAccessCard } from "@flinkk/edge-case/no-access-card";

export default async function CampaignsListPage(props: any) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters ?? []);

  // Get campaigns data with permissions
  const result = await getCampaigns({
    ...search,
    filters: validFilters,
  });

  if (!Boolean(result?.permissions?.canView)) {
    return <NoAccessCard />;
  }

  if (result.data.length === 0 && Object.keys(searchParams).length === 0) {
    return (
      <NoDataCard
        title="No Campaigns Created"
        description="You haven’t launched any campaigns yet. Start a new campaign to engage your contacts and track performance."
        href="/campaigns/new"
        ctaName="Create Campaign"
      />
    );
  }

  return (
    <CampaignsTable
      data={result.data}
      pageCount={result.pageCount}
      permissions={result.permissions}
    />
  );
}
