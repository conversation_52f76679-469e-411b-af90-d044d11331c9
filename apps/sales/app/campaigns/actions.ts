"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import type { GetCampaignsSchema } from "./_lib/validations";
import { campaignsTableFields } from "shared-constant-table-fields";

export async function getCampaigns(input: GetCampaignsSchema) {
  // Get all permissions for campaigns model
  const permissions = await getModelPermissions("campaign");

  // Check if user has permission to read campaigns
  if (!permissions.canView) {
    return {
      data: [],
      pageCount: 0,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      },
    };
  }

  const whereConditions = [
    input.name
      ? { name: { contains: input.name, mode: "insensitive" } }
      : undefined,
    input.status ? { status: input.status } : undefined,
  ].filter(Boolean);

  const data = await baseQuery(
    "campaign",
    {
      ...input,
      tableFields: campaignsTableFields,
    },
    whereConditions,
  );

  // Return both data and permissions for client-side use
  return {
    ...data,
    permissions,
  };
}
