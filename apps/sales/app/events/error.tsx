"use client";

import * as React from "react";
import { ErrorLoadingCard } from "@flinkk/edge-case/error-loading-card";

export default function LeadsError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  React.useEffect(() => {
    // Log the error to an error reporting service
    console.error("Leads page error:", error);
  }, [error]);

  // Generic error for other cases
  return <ErrorLoadingCard buttonClick={reset} />;
}
