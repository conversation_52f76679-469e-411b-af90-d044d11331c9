import * as React from "react";
import { AccountsTable } from "./page-client";
import { getAccounts } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";
import { NoAccessCard } from "@flinkk/edge-case/no-access-card";

export default async function ListPage(props: any) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters ?? []);

  const result = await getAccounts({
    ...search,
    filters: validFilters,
  });

  if (!Boolean(result?.permissions?.canView)) {
    return <NoAccessCard />;
  }

  if (result.data.length === 0 && Object.keys(searchParams).length === 0) {
    return (
      <NoDataCard
        title="No Accounts Available"
        description="Accounts help you group and manage related contacts or organizations. Add a new account to get started."
        href="/accounts/new"
        ctaName="Create Account"
      />
    );
  }

  return (
    <AccountsTable
      data={result.data}
      pageCount={result.pageCount}
      permissions={result.permissions}
    />
  );
}
