"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import type { GetAccountsSchema } from "./_lib/validations";
import { accountsTableFields } from "shared-constant-table-fields";

export async function getAccounts(input: GetAccountsSchema) {
  // Get all permissions for business accounts model
  const permissions = await getModelPermissions("Companies");

  // Check if user has permission to read accounts
  if (!permissions.canView) {
    return {
      data: [],
      pageCount: 0,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      },
    };
  }

  const whereConditions = [
    input.name
      ? { name: { contains: input.name, mode: "insensitive" } }
      : undefined,
    input.industry
      ? { industry: { contains: input.industry, mode: "insensitive" } }
      : undefined,
    input.type ? { type: input.type } : undefined,
  ].filter(Boolean);

  // Use base query utility which should be enhanced to use RBAC-enabled Prisma client
  const data = await baseQuery(
    "businessAccount",
    {
      ...input,
      tableFields: accountsTableFields,
    },
    whereConditions,
  );

  // Return both data and permissions for client-side use
  return {
    ...data,
    permissions,
  };
}

export async function getValueRange() {
  return { min: 0, max: 100 };
}
