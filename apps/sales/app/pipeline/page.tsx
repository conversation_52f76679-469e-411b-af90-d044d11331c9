import React from "react";
import { getPipelineData } from "./actions";
import { PipelineClient } from "./page-client";

export interface Opportunity {
  id: string;
  name: string;
  amount?: number | null;
  stage: string;
  closeDate?: string | null;
  probability?: number | null;
  accountId?: string | null;
  account?: {
    id: string;
    name?: string | null;
  } | null;
  contactId?: string | null;
  contact?: {
    id: string;
    name?: string | null;
    email?: string | null;
  } | null;
  userId: string;
  user?: {
    id: string;
    name?: string | null;
    email?: string | null;
    image?: string | null;
  } | null;
  createdAt: string;
  updatedAt: string;
}

export interface PipelineStage {
  stage: string;
  label: string;
  opportunities: Opportunity[];
}

export default async function PipelinePage({
  searchParams,
}: {
  searchParams: Promise<{
    search?: string;
    userId?: string;
    accountId?: string;
  }>;
}) {
  
  // Await searchParams
  const params = await searchParams;

  // Get initial pipeline data
  const initialPipelineData = await getPipelineData({
    search: params.search,
    userId: params.userId,
    accountId: params.accountId,
  });

  return <PipelineClient initialData={initialPipelineData} />;
}
