import * as React from "react";
import { ContactsTable } from "./page-client";
import { getContacts } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";
import { NoAccessCard } from "@flinkk/edge-case/no-access-card";

export default async function ListPage(props: any) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters ?? []);

  // Get contacts data with permissions
  const result = await getContacts({
    ...search,
    filters: validFilters,
  });

  if (!Boolean(result?.permissions?.canView)) {
    return <NoAccessCard />;
  }

  if (
    result.data.length === 0 &&
    Object.keys(searchParams).length === 0
  ) {
    return (
      <NoDataCard
        title="No Contacts Found"
        description="You haven’t added any contacts yet. Create a new contact to begin managing your CRM relationships."
        href="/contacts/new"
        ctaName="Create Contact"
      />
    );
  }

  return (
    <ContactsTable
      data={result.data}
      pageCount={result.pageCount}
      permissions={result.permissions}
    />
  );
}
