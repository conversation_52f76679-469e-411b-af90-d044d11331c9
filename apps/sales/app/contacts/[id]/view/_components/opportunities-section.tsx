"use client";

import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import { <PERSON>, CardContent, CardHeader } from "@flinkk/components/ui/card";
import { Typography } from "@flinkk/components/ui/typography";
import { Badge } from "@flinkk/components/ui/badge";
import {
  Toolt<PERSON>,
  Tooltip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@flinkk/components/ui/tooltip";

import { useRouter } from "next/navigation";
import { PlusIcon, FileTextIcon, Trash2Icon } from "lucide-react";
import { useEffect, useState } from "react";
import { useDelete } from "@flinkk/hooks/mutation/use-delete";
import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog
    ),
  {
    ssr: false,
  }
);

interface Opportunity {
  id: string;
  dealName: string;
  description?: string;
  stage: string;
  status: string;
  value?: number;
  currency?: string;
  probability?: number;
  expectedCloseDate?: string;
  createdAt: string;
  updatedAt: string;
  account?: {
    id: string;
    name: string;
  };
  user?: {
    id: string;
    name: string;
    email: string;
  };
}

interface OpportunitiesSectionProps {
  contactId: string;
  contactName: string;
}

export const OpportunitiesSection = ({
  contactId,
  contactName,
}: OpportunitiesSectionProps) => {
  const router = useRouter();
  const [opportunities, setOpportunities] = useState<Opportunity[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [opportunityToDelete, setOpportunityToDelete] =
    useState<Opportunity | null>(null);

  // Initialize delete hook
  const { deleteRecord: deleteOpportunity } = useDelete({
    model: "opportunity",
    onSuccess: () => {
      // Remove the deleted opportunity from the local state
      if (opportunityToDelete) {
        setOpportunities((prev) =>
          prev.filter(
            (opportunity) => opportunity.id !== opportunityToDelete.id,
          ),
        );
        setOpportunityToDelete(null);
        setDeleteDialogOpen(false);
      }
    },
  });

  // Fetch opportunities for this contact
  useEffect(() => {
    const fetchOpportunities = async () => {
      try {
        setLoading(true);
        const response = await fetch(
          `/api/contacts/${contactId}/opportunities`,
        );

        if (!response.ok) {
          throw new Error("Failed to fetch opportunities");
        }

        const data = await response.json();
        setOpportunities(data.data || []);
      } catch (err) {
        console.error("Error fetching opportunities:", err);
        setError("Failed to load opportunities");
      } finally {
        setLoading(false);
      }
    };

    if (contactId) {
      fetchOpportunities();
    }
  }, [contactId]);

  const handleCreateOpportunity = () => {
    // Navigate to opportunity creation with contact pre-filled
    router.push(
      `/opportunities/new?contactId=${contactId}&title=${encodeURIComponent(`New Opportunity for ${contactName}`)}`,
    );
  };

  const handleViewOpportunity = (opportunityId: string) => {
    router.push(
      `/opportunities/${opportunityId}/view?title=${encodeURIComponent(contactName)}`,
    );
  };

  const handleDeleteClick = (
    opportunity: Opportunity,
    event: React.MouseEvent,
  ) => {
    event.stopPropagation(); // Prevent navigation when clicking delete
    setOpportunityToDelete(opportunity);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (opportunityToDelete) {
      await deleteOpportunity(opportunityToDelete.id);
    }
  };

  const formatCurrency = (value?: number, currency?: string) => {
    if (!value) return "N/A";
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency || "USD",
    }).format(value);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "open":
        return "bg-green-100 text-green-800";
      case "closed_won":
        return "bg-blue-100 text-blue-800";
      case "closed_lost":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Typography type="h3" className="font-semibold">
              Opportunities
            </Typography>
            <Button
              onClick={handleCreateOpportunity}
              size="sm"
              className="gap-2 bg-teal-600 hover:bg-teal-700 text-white"
            >
              <PlusIcon className="h-4 w-4" />
              Create Opportunity
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Typography type="p" className="text-sm text-gray-500">
              Loading opportunities...
            </Typography>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Typography type="h3" className="font-semibold">
              Opportunities
            </Typography>
            <Button
              onClick={handleCreateOpportunity}
              size="sm"
              className="gap-2 bg-teal-600 hover:bg-teal-700 text-white"
            >
              <PlusIcon className="h-4 w-4" />
              Create Opportunity
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Typography type="p" className="text-sm text-red-500">
              {error}
            </Typography>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-fit">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <Typography type="h3" className="font-semibold">
            Opportunities ({opportunities.length})
          </Typography>
          <Button
            onClick={handleCreateOpportunity}
            size="sm"
            className="gap-2 bg-teal-600 hover:bg-teal-700 text-white"
          >
            <PlusIcon className="h-4 w-4" />
            Create Opportunity
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {opportunities.length === 0 ? (
          /* Empty state */
          <div className="flex flex-col items-center justify-center p-8 space-y-4 border-2 border-dashed border-gray-200 rounded-lg">
            <div className="p-3 bg-gray-100 rounded-lg">
              <FileTextIcon className="h-8 w-8 text-gray-400" />
            </div>
            <div className="text-center space-y-2">
              <Typography type="h4" className="font-medium text-gray-900">
                No opportunities created yet
              </Typography>
              <Typography type="p" className="text-sm text-gray-500 max-w-sm">
                Create your first opportunity for this contact to get started
                with the sales process.
              </Typography>
            </div>
          </div>
        ) : (
          /* Opportunities list */
          <div className="space-y-3">
            {opportunities.map((opportunity) => (
              <div
                key={opportunity.id}
                className="p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors cursor-pointer"
                onClick={() => handleViewOpportunity(opportunity.id)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Typography
                        type="h4"
                        className="font-medium text-gray-900"
                      >
                        {opportunity.dealName}
                      </Typography>
                      <Badge className={getStatusColor(opportunity.status)}>
                        {opportunity.status.replace("_", " ")}
                      </Badge>
                    </div>

                    {opportunity.description && (
                      <Typography
                        type="p"
                        className="text-sm text-gray-600 mb-2 line-clamp-2"
                      >
                        {opportunity.description}
                      </Typography>
                    )}

                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <span>Stage: {opportunity.stage}</span>
                      <span>
                        Value:{" "}
                        {formatCurrency(
                          opportunity.value,
                          opportunity.currency,
                        )}
                      </span>
                      {opportunity.probability && (
                        <span>Probability: {opportunity.probability}%</span>
                      )}
                    </div>
                  </div>

                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-400 hover:text-red-600 hover:bg-red-50 ml-2 flex-shrink-0"
                          onClick={(e) => handleDeleteClick(opportunity, e)}
                        >
                          <Trash2Icon className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Delete Opportunity</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onDelete={handleConfirmDelete}
        title="Are you sure you want to delete this opportunity?"
        description={`This action cannot be undone. This will permanently delete the opportunity "${opportunityToDelete?.dealName}" and remove all associated data.`}
      />
    </Card>
  );
};
