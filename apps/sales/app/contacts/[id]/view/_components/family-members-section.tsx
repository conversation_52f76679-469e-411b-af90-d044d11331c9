"use client";

import React, { useEffect, useState } from "react";
import { <PERSON>, CardContent, CardHeader } from "@flinkk/components/ui/card";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Badge } from "@flinkk/components/ui/badge";
import { Typography } from "@flinkk/components/ui/typography";
import { PlusIcon, UsersIcon, EditIcon, Trash2Icon, CalendarIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useDelete } from "@flinkk/hooks/mutation/use-delete";
import { AddFamilyDialog } from "./family-dialogs/add-family-dialog";
import { EditFamilyDialog } from "./family-dialogs/edit-family-dialog";
import { AddFamilyMemberDialog } from "./family-dialogs/add-family-member-dialog";
import { EditFamilyMemberDialog } from "./family-dialogs/edit-family-member-dialog";


import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog
    ),
  {
    ssr: false,
  }
);

interface FamilyMember {
  id: string;
  name: string;
  dob: string;
  relationship: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface Family {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  members: FamilyMember[];
  contact: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
}

interface FamilyMembersSectionProps {
  contactId: string;
  contactName: string;
}

export const FamilyMembersSection = ({
  contactId,
  contactName,
}: FamilyMembersSectionProps) => {
  const router = useRouter();
  const [families, setFamilies] = useState<Family[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Dialog states
  const [addFamilyDialogOpen, setAddFamilyDialogOpen] = useState(false);
  const [editFamilyDialogOpen, setEditFamilyDialogOpen] = useState(false);
  const [addMemberDialogOpen, setAddMemberDialogOpen] = useState(false);
  const [editMemberDialogOpen, setEditMemberDialogOpen] = useState(false);
  
  // Delete states
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [familyToDelete, setFamilyToDelete] = useState<Family | null>(null);
  const [memberToDelete, setMemberToDelete] = useState<FamilyMember | null>(null);
  
  // Edit states
  const [familyToEdit, setFamilyToEdit] = useState<Family | null>(null);
  const [memberToEdit, setMemberToEdit] = useState<FamilyMember | null>(null);
  const [selectedFamilyId, setSelectedFamilyId] = useState<string | null>(null);

  // Delete hooks
  const {
    mutate: deleteFamily,
    isPending: isDeletingFamily,
  } = useDelete({
    endpoint: "/api/crm/families",
    onSuccess: () => {
      setDeleteDialogOpen(false);
      setFamilyToDelete(null);
      fetchFamilies();
    },
    onError: (error) => {
      console.error("Error deleting family:", error);
    },
  });

  const {
    mutate: deleteFamilyMember,
    isPending: isDeletingMember,
  } = useDelete({
    endpoint: "/api/crm/family-members",
    onSuccess: () => {
      setDeleteDialogOpen(false);
      setMemberToDelete(null);
      fetchFamilies();
    },
    onError: (error) => {
      console.error("Error deleting family member:", error);
    },
  });

  // Fetch families for this contact
  const fetchFamilies = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/crm/contacts/${contactId}/families`);

      if (!response.ok) {
        throw new Error("Failed to fetch families");
      }

      const data = await response.json();
      setFamilies(data.data || []);
    } catch (err) {
      console.error("Error fetching families:", err);
      setError("Failed to load families");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (contactId) {
      fetchFamilies();
    }
  }, [contactId]);

  const handleAddFamily = () => {
    setAddFamilyDialogOpen(true);
  };

  const handleEditFamily = (family: Family) => {
    setFamilyToEdit(family);
    setEditFamilyDialogOpen(true);
  };

  const handleDeleteFamily = (family: Family, event: React.MouseEvent) => {
    event.stopPropagation();
    setFamilyToDelete(family);
    setDeleteDialogOpen(true);
  };

  const handleAddMember = (familyId: string) => {
    setSelectedFamilyId(familyId);
    setAddMemberDialogOpen(true);
  };

  const handleEditMember = (member: FamilyMember) => {
    setMemberToEdit(member);
    setEditMemberDialogOpen(true);
  };

  const handleDeleteMember = (member: FamilyMember, event: React.MouseEvent) => {
    event.stopPropagation();
    setMemberToDelete(member);
    setDeleteDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (familyToDelete) {
      await deleteFamily(familyToDelete.id);
    } else if (memberToDelete) {
      await deleteFamilyMember(memberToDelete.id);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const calculateAge = (dobString: string) => {
    const dob = new Date(dobString);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }
    
    return age;
  };

  const getRelationshipColor = (relationship: string) => {
    switch (relationship) {
      case "SPOUSE":
        return "bg-pink-100 text-pink-800 dark:bg-pink-900 dark:text-pink-300";
      case "CHILD":
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300";
      case "INFANT":
        return "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300";
      case "PARENT":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300";
      case "OTHER":
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300";
    }
  };

  if (loading) {
    return (
      <Card className="h-fit">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Typography type="p" className="text-gray-500">
              Loading families...
            </Typography>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="h-fit">
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <Typography type="p" className="text-red-500">
              {error}
            </Typography>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card className="h-fit">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <Typography type="h3" className="font-semibold">
              Family Members ({families.reduce((total, family) => total + family.members.length, 0)})
            </Typography>
            <Button
              onClick={handleAddFamily}
              size="sm"
              className="gap-2 bg-teal-600 hover:bg-teal-700 text-white"
            >
              <PlusIcon className="h-4 w-4" />
              Add Family Group
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {families.length === 0 ? (
            /* Empty state */
            <div className="flex flex-col items-center justify-center p-8 space-y-4 border-2 border-dashed border-gray-200 rounded-lg">
              <div className="p-3 bg-gray-100 rounded-lg">
                <UsersIcon className="h-8 w-8 text-gray-400" />
              </div>
              <div className="text-center space-y-2">
                <Typography type="h4" className="font-medium text-gray-900">
                  No family groups created yet
                </Typography>
                <Typography type="p" className="text-sm text-gray-500 max-w-sm">
                  Create your first family group for this contact to start managing family member information.
                </Typography>
              </div>
            </div>
          ) : (
            /* Families list */
            <div className="space-y-6">
              {families.map((family) => (
                <div key={family.id} className="border border-gray-200 rounded-lg p-4 space-y-4">
                  {/* Family Header */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Typography type="h4" className="font-medium text-gray-900">
                        {family.name}
                      </Typography>
                      <Typography type="p" className="text-sm text-gray-500">
                        Created on {formatDate(family.createdAt)}
                      </Typography>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditFamily(family)}
                        className="gap-1"
                      >
                        <EditIcon className="h-3 w-3" />
                        Rename
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => handleDeleteFamily(family, e)}
                        className="gap-1 text-red-600 hover:text-red-700"
                      >
                        <Trash2Icon className="h-3 w-3" />
                        Delete
                      </Button>
                    </div>
                  </div>

                  {/* Family Members */}
                  {family.members.length === 0 ? (
                    <div className="text-center py-4 border-2 border-dashed border-gray-200 rounded-lg">
                      <Typography type="p" className="text-sm text-gray-500 mb-2">
                        No family members added yet
                      </Typography>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleAddMember(family.id)}
                        className="gap-1"
                      >
                        <PlusIcon className="h-3 w-3" />
                        Add Member
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {family.members.map((member) => (
                        <div
                          key={member.id}
                          className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <Typography type="h5" className="font-medium text-gray-900">
                                {member.name}
                              </Typography>
                              <Badge className={getRelationshipColor(member.relationship)}>
                                {member.relationship.replace("_", " ")}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-4 text-sm text-gray-600">
                              <div className="flex items-center gap-1">
                                <CalendarIcon className="h-3 w-3" />
                                <span>Age: {calculateAge(member.dob)}</span>
                              </div>
                              <span>DOB: {formatDate(member.dob)}</span>
                            </div>
                            {member.notes && (
                              <Typography type="p" className="text-sm text-gray-600 mt-1">
                                {member.notes}
                              </Typography>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditMember(member)}
                              className="gap-1"
                            >
                              <EditIcon className="h-3 w-3" />
                              Edit
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => handleDeleteMember(member, e)}
                              className="gap-1 text-red-600 hover:text-red-700"
                            >
                              <Trash2Icon className="h-3 w-3" />
                              Delete
                            </Button>
                          </div>
                        </div>
                      ))}
                      
                      {/* Add Member Button */}
                      <div className="pt-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAddMember(family.id)}
                          className="gap-1 w-full"
                        >
                          <PlusIcon className="h-3 w-3" />
                          Add Member
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialogs */}
      <AddFamilyDialog
        open={addFamilyDialogOpen}
        onOpenChange={setAddFamilyDialogOpen}
        contactId={contactId}
        onSuccess={fetchFamilies}
      />

      <EditFamilyDialog
        open={editFamilyDialogOpen}
        onOpenChange={setEditFamilyDialogOpen}
        family={familyToEdit}
        onSuccess={fetchFamilies}
      />

      <AddFamilyMemberDialog
        open={addMemberDialogOpen}
        onOpenChange={setAddMemberDialogOpen}
        familyId={selectedFamilyId}
        onSuccess={fetchFamilies}
      />

      <EditFamilyMemberDialog
        open={editMemberDialogOpen}
        onOpenChange={setEditMemberDialogOpen}
        member={memberToEdit}
        onSuccess={fetchFamilies}
      />

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleConfirmDelete}
        title={familyToDelete ? "Delete Family Group" : "Delete Family Member"}
        description={
          familyToDelete
            ? `Are you sure you want to delete "${familyToDelete.name}"? This will also delete all family members in this group. This action cannot be undone.`
            : `Are you sure you want to delete "${memberToDelete?.name}"? This action cannot be undone.`
        }
        isLoading={isDeletingFamily || isDeletingMember}
      />
    </>
  );
};
