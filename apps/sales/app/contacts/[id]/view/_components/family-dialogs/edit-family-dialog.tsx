"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@flinkk/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@flinkk/components/ui/form";
import { Input } from "@flinkk/components/ui/input";
import { Button } from "@flinkk/components/ui/button";
import { toast } from "sonner";

const editFamilySchema = z.object({
  name: z.string().min(1, "Family name is required"),
});

type EditFamilyFormValues = z.infer<typeof editFamilySchema>;

interface Family {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

interface EditFamilyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  family: Family | null;
  onSuccess: () => void;
}

export const EditFamilyDialog = ({
  open,
  onOpenChange,
  family,
  onSuccess,
}: EditFamilyDialogProps) => {
  const [isLoading, setIsLoading] = React.useState(false);

  const form = useForm<EditFamilyFormValues>({
    resolver: zodResolver(editFamilySchema),
    defaultValues: {
      name: "",
    },
  });

  // Update form when family changes
  React.useEffect(() => {
    if (family) {
      form.reset({
        name: family.name,
      });
    }
  }, [family, form]);

  const onSubmit = async (values: EditFamilyFormValues) => {
    if (!family) return;

    try {
      setIsLoading(true);

      const response = await fetch(`/api/crm/families/${family.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: values.name,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update family");
      }

      const data = await response.json();
      
      toast.success("Family group updated successfully");
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      console.error("Error updating family:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update family");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Family Group</DialogTitle>
          <DialogDescription>
            Update the name of this family group.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Family Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Smith Family"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Updating..." : "Update Family"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
