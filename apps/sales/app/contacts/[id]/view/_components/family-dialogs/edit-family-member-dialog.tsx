"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@flinkk/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@flinkk/components/ui/form";
import { Input } from "@flinkk/components/ui/input";
import { Textarea } from "@flinkk/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import { Button } from "@flinkk/components/ui/button";
import { toast } from "sonner";

const editFamilyMemberSchema = z.object({
  name: z.string().min(1, "Name is required"),
  dob: z.string().refine((date) => {
    const parsedDate = new Date(date);
    const today = new Date();
    return parsedDate <= today;
  }, "Date of birth must be in the past"),
  relationship: z.enum(["SPOUSE", "CHILD", "INFANT", "PARENT", "OTHER"], {
    required_error: "Please select a relationship",
  }),
  notes: z.string().optional(),
});

type EditFamilyMemberFormValues = z.infer<typeof editFamilyMemberSchema>;

interface FamilyMember {
  id: string;
  name: string;
  dob: string;
  relationship: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

interface EditFamilyMemberDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  member: FamilyMember | null;
  onSuccess: () => void;
}

const relationshipOptions = [
  { value: "SPOUSE", label: "Spouse" },
  { value: "CHILD", label: "Child" },
  { value: "INFANT", label: "Infant" },
  { value: "PARENT", label: "Parent" },
  { value: "OTHER", label: "Other" },
];

export const EditFamilyMemberDialog = ({
  open,
  onOpenChange,
  member,
  onSuccess,
}: EditFamilyMemberDialogProps) => {
  const [isLoading, setIsLoading] = React.useState(false);

  const form = useForm<EditFamilyMemberFormValues>({
    resolver: zodResolver(editFamilyMemberSchema),
    defaultValues: {
      name: "",
      dob: "",
      notes: "",
    },
  });

  // Update form when member changes
  React.useEffect(() => {
    if (member) {
      const dobDate = new Date(member.dob);
      const formattedDob = dobDate.toISOString().split('T')[0];
      
      form.reset({
        name: member.name,
        dob: formattedDob,
        relationship: member.relationship as any,
        notes: member.notes || "",
      });
    }
  }, [member, form]);

  const onSubmit = async (values: EditFamilyMemberFormValues) => {
    if (!member) return;

    try {
      setIsLoading(true);

      const response = await fetch(`/api/crm/family-members/${member.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: values.name,
          dob: values.dob,
          relationship: values.relationship,
          notes: values.notes || undefined,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to update family member");
      }

      const data = await response.json();
      
      toast.success("Family member updated successfully");
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      console.error("Error updating family member:", error);
      toast.error(error instanceof Error ? error.message : "Failed to update family member");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Family Member</DialogTitle>
          <DialogDescription>
            Update the information for this family member.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Full Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter full name"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="dob"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Date of Birth</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                        disabled={isLoading}
                        max={new Date().toISOString().split('T')[0]}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="relationship"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Relationship</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select relationship" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {relationshipOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional notes about this family member"
                      className="resize-none"
                      rows={3}
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Updating..." : "Update Member"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
