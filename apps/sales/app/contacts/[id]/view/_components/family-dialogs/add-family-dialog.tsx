"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@flinkk/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@flinkk/components/ui/form";
import { Input } from "@flinkk/components/ui/input";
import { Button } from "@flinkk/components/ui/button";
import { toast } from "sonner";

const addFamilySchema = z.object({
  name: z.string().min(1, "Family name is required"),
});

type AddFamilyFormValues = z.infer<typeof addFamilySchema>;

interface AddFamilyDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  contactId: string;
  onSuccess: () => void;
}

export const AddFamilyDialog = ({
  open,
  onOpenChange,
  contactId,
  onSuccess,
}: AddFamilyDialogProps) => {
  const [isLoading, setIsLoading] = React.useState(false);

  const form = useForm<AddFamilyFormValues>({
    resolver: zodResolver(addFamilySchema),
    defaultValues: {
      name: "",
    },
  });

  const onSubmit = async (values: AddFamilyFormValues) => {
    try {
      setIsLoading(true);

      const response = await fetch("/api/crm/families", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: values.name,
          contactId: contactId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create family");
      }

      const data = await response.json();
      
      toast.success("Family group created successfully");
      form.reset();
      onOpenChange(false);
      onSuccess();
    } catch (error) {
      console.error("Error creating family:", error);
      toast.error(error instanceof Error ? error.message : "Failed to create family");
    } finally {
      setIsLoading(false);
    }
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      form.reset();
    }
    onOpenChange(newOpen);
  };

  return (
    <Dialog open={open} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Family Group</DialogTitle>
          <DialogDescription>
            Create a new family group to organize family members.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Family Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="e.g., Smith Family"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => handleOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? "Creating..." : "Create Family"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};
