import { getContactById } from "../actions";
import { Header } from "./_components/header";
import { CopilotKit } from "@copilotkit/react-core";
import { CollapsibleLeadScreen } from "@flinkk/copilot-wrapper";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { CustomSession } from "@flinkk/shared-auth/options";

export default async function FormPageLayout({
  children,
  params,
}: {
  children: React.ReactNode;
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const contactDetails = await getContactById(id);

  if (!contactDetails) {
    throw new Error("Contact not found");
  }

  const session = (await getServerSession()) as CustomSession;

  return (
    <CopilotKit runtimeUrl="/api/copilot/contact-agent">
      <div className="w-full h-svh flex overflow-hidden">
        {/* Main Content Area - Single Scroll View */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Fixed Header Section */}
          <div className="flex-shrink-0 px-4 pt-4 pb-2 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <Header contact={contactDetails} contactId={id} />
          </div>

          {/* Scrollable Content Area */}
          <div className="flex-1 overflow-y-auto px-4 py-6">
            <div className="max-w-6xl mx-auto space-y-6">
              {children}
            </div>
          </div>
        </div>

        {/* Collapsible Bailey AI */}
        <CollapsibleLeadScreen session={session} leadId={id} />
      </div>
    </CopilotKit>
  );
}
