"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import type { GetContactsSchema } from "./_lib/validations";
import { contactsTableFields } from "shared-constant-table-fields";

export async function getContacts(input: GetContactsSchema) {
  // Get all permissions for contacts model
  const permissions = await getModelPermissions("contact");

  console.log("Permissions for contacts:", permissions);

  // Check if user has permission to read contacts
    if (!permissions.canView) {
      return {
        data: [],
        pageCount: 0,
        permissions: {
          canView: false,
          canCreate: false,
          canEdit: false,
          canDelete: false,
        },
      };
    }

  const whereConditions = [
    input.firstName
      ? { firstName: { contains: input.firstName, mode: "insensitive" } }
      : undefined,
    input.lastName
      ? { lastName: { contains: input.lastName, mode: "insensitive" } }
      : undefined,
    input.email
      ? { email: { contains: input.email, mode: "insensitive" } }
      : undefined,
    input.phoneNumber
      ? { phoneNumber: { contains: input.phoneNumber, mode: "insensitive" } }
      : undefined,
    input.role
      ? { role: { contains: input.role, mode: "insensitive" } }
      : undefined,
    input.source
      ? { source: { contains: input.source, mode: "insensitive" } }
      : undefined,
  ].filter(Boolean);

  // Use base query utility which should be enhanced to use RBAC-enabled Prisma client
  const data = await baseQuery(
    "contact",
    {
      ...input,
      tableFields: contactsTableFields,
    },
    whereConditions,
  );

  // Return both data and permissions for client-side use
  return {
    ...data,
    permissions,
  };
}

export async function getValueRange() {
  return { min: 0, max: 100 };
}
