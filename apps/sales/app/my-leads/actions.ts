"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import type { GetLeadsSchema } from "./_lib/validations";
import { leadsTableFields } from "shared-constant-table-fields";

export async function getLeads(input: GetLeadsSchema) {
  // Get current user session
  const { userId } = await getServerSession();

  // Get all permissions for leads model
  const permissions = await getModelPermissions("lead");

  // Check if user has permission to read leads
  if (!permissions.canView) {
    return {
      data: [],
      pageCount: 0,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      },
    };
  }

  // Build where conditions
  const whereConditions = [
    { userId: userId }, // Filter leads assigned to current user
    input.firstName
      ? {
          OR: [
            { firstName: { contains: input.firstName, mode: "insensitive" } },
            { lastName: { contains: input.firstName, mode: "insensitive" } },
            { email: { contains: input.firstName, mode: "insensitive" } },
            { company: { contains: input.firstName, mode: "insensitive" } },
          ],
        }
      : undefined,
    input.stage ? { stage: input.stage } : undefined,
  ].filter(Boolean);

  // Use base query utility which handles field selection and related fields automatically
  // Note: baseQuery should be enhanced to use RBAC-enabled Prisma client
  const data = await baseQuery(
    "lead",
    {
      ...input,
      tableFields: leadsTableFields,
    },
    whereConditions
  );

  // Return both data and permissions for client-side use
  return {
    ...data,
    permissions,
  };
}

export async function getValueRange() {
  return { min: 0, max: 100 };
}
