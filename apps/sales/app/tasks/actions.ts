"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import type { GetTasksSchema } from "./_lib/validations";
import { tasksTableFields } from "shared-constant-table-fields";

export async function getTasks(input: GetTasksSchema) {
  // Get all permissions for tasks model
  const permissions = await getModelPermissions("task");

  // Check if user has permission to read tasks
  if (!permissions.canView) {
    return {
      data: [],
      pageCount: 0,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      },
    };
  }

  const whereConditions = [
    input.title
      ? { title: { contains: input.title, mode: "insensitive" } }
      : undefined,
    input.status ? { status: input.status } : undefined,
    input.priority ? { priority: input.priority } : undefined,
  ].filter(Boolean);

  const data = await baseQuery(
    "task",
    {
      ...input,
      tableFields: tasksTableFields,
    },
    whereConditions,
  );

  // Return both data and permissions for client-side use
  return {
    ...data,
    permissions,
  };
}

export async function getValueRange() {
  return { min: 0, max: 100 };
}
