import * as React from "react";
import { TasksTable } from "./page-client";
import { getTasks } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";
import { NoAccessCard } from "@flinkk/edge-case/no-access-card";

export default async function ListPage(props: any) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters ?? []);

  // Get tasks data with permissions
  const result = await getTasks({
    ...search,
    filters: validFilters,
  });

  if (!Boolean(result?.permissions?.canView)) {
    return <NoAccessCard />;
  }

  if (result.data.length === 0 && Object.keys(searchParams).length === 0) {
    return (
      <NoDataCard
        title="No Tasks Assigned"
        description="You don’t have any tasks at the moment. Tasks will appear here when created or assigned to you."
      />
    );
  }

  return (
    <TasksTable
      data={result.data}
      pageCount={result.pageCount}
      permissions={result.permissions}
    />
  );
}
