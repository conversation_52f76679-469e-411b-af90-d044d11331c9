"use client";

import type { Task } from "@/types/task";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import * as React from "react";

import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableSortList } from "@flinkk/data-table/component/data-table-sort-list";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDelete } from "@flinkk/hooks";
import { getTasksTableColumns } from "./columns";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog
    ),
  {
    ssr: false,
  }
);

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface TasksTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
}

export function TasksTable({ data, pageCount, permissions }: TasksTableProps) {
  const router = useRouter();
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<Task> | null>(null);

  const { deleteRecord } = useDelete({
    model: "task",
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      setRowAction(null);
      router.refresh();
    },
  });

  const handleDeleteTask = async () => {
    if (rowAction?.type === "delete" && rowAction.row) {
      await deleteRecord(rowAction.row.id);
    }
  };

  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setIsDeleteDialogOpen(true);
    }
  }, [rowAction]);

  const columns = React.useMemo(
    () =>
      getTasksTableColumns({
        setRowAction,
      }),
    [setRowAction],
  );

  const { table } = useDataTable({
    data,
    columns,
    pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  return (
    <>
      <DataTable
        table={table}
        onRowClick={(row) => {
          // Use task subject for the tab title
          const taskTitle = row.subject || "Unnamed Task";

          // Create URL with title parameter
          const url = `/tasks/${row.id}/view?title=${encodeURIComponent(taskTitle)}`;
          router.push(url);
        }}
        doctype="task"
      >
        <DataTableToolbar
          table={table}
          buttonText={permissions.canCreate ? "New Task" : undefined}
          href={permissions.canCreate ? "/tasks/new" : undefined}
        >
          <DataTableSortList table={table} align="end" />
        </DataTableToolbar>
      </DataTable>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onDelete={handleDeleteTask}
        />
      )}
    </>
  );
}
