"use server";

import { prisma } from "@flinkk/database/prisma";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { getModelPermissions } from "@flinkk/shared-rbac";
import type { GetPackagesSchema } from "./_lib/validations";

// Get packages with filtering and pagination
export async function getPackages(input: GetPackagesSchema) {
  // Get all permissions for packages model
  const permissions = await getModelPermissions("Packages");

  // Check if user has permission to read packages
  if (!permissions.canView) {
    return {
      data: [],
      pageCount: 0,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      },
    };
  }

  const { tenantId } = await getServerSession();

  if (!tenantId) {
    throw new Error("Unauthorized");
  }

  const safeInput = {
    page: 1,
    perPage: 10,
    sort: [{ id: "createdAt", desc: true }],
    filters: [],
    name: "",
    sku: "",
    search: "",
    status: "",
    preferredVendor: "",
    createdBy: "",
    ...input,
  };

  // Ensure valid pagination values
  const page = Math.max(1, Number(safeInput.page) || 1);
  const perPage = Math.max(1, Number(safeInput.perPage) || 10);
  const skip = (page - 1) * perPage;

  // Build where conditions
  const whereConditions = [
    { deleted: false }, // Only show non-deleted packages
    { tenantId }, // Tenant isolation
    safeInput.name
      ? { name: { contains: safeInput.name, mode: "insensitive" } }
      : undefined,
    safeInput.sku
      ? { sku: { contains: safeInput.sku, mode: "insensitive" } }
      : undefined,
    safeInput.search
      ? {
          OR: [
            { name: { contains: safeInput.search, mode: "insensitive" } },
            {
              description: { contains: safeInput.search, mode: "insensitive" },
            },
            { sku: { contains: safeInput.search, mode: "insensitive" } },
            {
              preferredVendor: {
                contains: safeInput.search,
                mode: "insensitive",
              },
            },
          ],
        }
      : undefined,
    safeInput.status ? { status: safeInput.status } : undefined,
    safeInput.preferredVendor
      ? {
          preferredVendor: {
            contains: safeInput.preferredVendor,
            mode: "insensitive",
          },
        }
      : undefined,
    safeInput.createdBy ? { createdById: safeInput.createdBy } : undefined,
  ].filter(Boolean);

  const where = {
    AND: whereConditions,
  };

  // Build orderBy
  const orderBy = safeInput.sort?.length
    ? safeInput.sort.map((item) => ({
        [item.id]: item.desc ? "desc" : "asc",
      }))
    : [{ createdAt: "desc" }];

  // Execute query with transaction
  const [data, total] = await prisma.$transaction([
    prisma.package.findMany({
      where,
      take: perPage,
      skip,
      orderBy,
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        updatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true,
          },
        },
        lines: {
          select: {
            id: true,
            lineNumber: true,
            description: true,
            quantity: true,
            unitPrice: true,
            lineTotal: true,
          },
          orderBy: {
            lineNumber: "asc",
          },
        },
      },
    }),
    prisma.package.count({ where }),
  ]);

  return {
    data,
    pageCount: Math.ceil(total / perPage),
    permissions,
  };
}

// Get a single package by ID
export async function getPackage(id: string) {
  const { tenantId } = await getServerSession();

  if (!tenantId) {
    throw new Error("Unauthorized");
  }

  const packageData = await prisma.package.findUnique({
    where: {
      id,
      tenantId,
      deleted: false,
    },
    include: {
      createdBy: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
      updatedBy: {
        select: {
          id: true,
          name: true,
          email: true,
          image: true,
        },
      },
      lines: {
        include: {
          product: {
            select: {
              id: true,
              name: true,
              sku: true,
              sellingPrice: true,
              type: true,
              unit: {
                select: {
                  id: true,
                  name: true,
                  displayName: true,
                },
              },
            },
          },
          unit: {
            select: {
              id: true,
              name: true,
              displayName: true,
            },
          },
        },
        orderBy: {
          lineNumber: "asc",
        },
      },
    },
  });

  if (!packageData) {
    throw new Error("Package not found");
  }

  return packageData;
}

// Create a new package
export async function createPackage(data: any) {
  const { tenantId, userId } = await getServerSession();

  if (!tenantId || !userId) {
    throw new Error("Unauthorized");
  }

  // Validate required fields
  if (!data.name || !data.lines || data.lines.length === 0) {
    throw new Error("Package name and at least one line item are required");
  }

  // Generate SKU if not provided
  const sku = data.sku || `PKG-${Date.now()}`;

  // Calculate package totals
  let subtotal = 0;
  const lineCalculations = data.lines.map((line, index) => {
    const lineTotalBeforeDiscount = line.quantity * line.unitPrice;
    let discount = 0;

    if (line.discountType === "PERCENTAGE") {
      discount = (lineTotalBeforeDiscount * (line.discountValue || 0)) / 100;
    } else {
      discount = line.discountValue || 0;
    }

    const lineTotal = lineTotalBeforeDiscount - discount;
    subtotal += lineTotal;

    return {
      lineNumber: index + 1,
      lineTotalBeforeDiscount,
      discount,
      lineTotal,
    };
  });

  // Calculate package-level totals
  const taxPercentage = data.taxPercentage || 0;
  const totalTax = (subtotal * taxPercentage) / 100;
  const grandTotal = subtotal + totalTax;

  // Create package with lines in a transaction
  const createdPackage = await prisma.$transaction(async (tx) => {
    // Create the package
    const newPackage = await tx.package.create({
      data: {
        name: data.name.trim(),
        description: data.description?.trim() || null,
        sku: sku.trim(),
        status: data.status || "DRAFT",
        subtotal,
        packageDiscount: 0,
        discountType: "PERCENTAGE",
        discountValue: 0,
        totalAfterDiscount: subtotal,
        totalTax,
        grandTotal,
        taxPercentage,
        preferredVendor: data.preferredVendor?.trim() || null,
        tenantId,
        createdById: userId,
      },
    });

    // Create package lines
    const packageLines = await Promise.all(
      data.lines.map((line, index) => {
        const calc = lineCalculations[index];
        return tx.packageLine.create({
          data: {
            lineNumber: calc.lineNumber,
            productId: line.productId || null,
            priceBookId: line.priceBookId || null,
            description: line.description.trim(),
            quantity: line.quantity,
            unitId: line.unitId || null,
            unitPrice: line.unitPrice,
            listPrice: line.listPrice || null,
            discount: calc.discount,
            discountType: line.discountType || "PERCENTAGE",
            discountValue: line.discountValue || 0,
            lineTotal: calc.lineTotal,
            lineTotalBeforeDiscount: calc.lineTotalBeforeDiscount,
            packageId: newPackage.id,
            tenantId,
          },
        });
      }),
    );

    return { ...newPackage, lines: packageLines };
  });

  return createdPackage;
}

// Update an existing package
export async function updatePackage(id: string, data: any) {
  const { tenantId, userId } = await getServerSession();

  if (!tenantId || !userId) {
    throw new Error("Unauthorized");
  }

  // Check if package exists and belongs to tenant
  const existingPackage = await prisma.package.findUnique({
    where: {
      id,
      tenantId,
      deleted: false,
    },
    include: {
      lines: true,
    },
  });

  if (!existingPackage) {
    throw new Error("Package not found");
  }

  // Calculate new totals if lines are provided
  let calculatedTotals = {};
  if (data.lines) {
    let subtotal = 0;
    const lineCalculations = data.lines.map((line, index) => {
      const lineTotalBeforeDiscount = line.quantity * line.unitPrice;
      let discount = 0;

      if (line.discountType === "PERCENTAGE") {
        discount = (lineTotalBeforeDiscount * (line.discountValue || 0)) / 100;
      } else {
        discount = line.discountValue || 0;
      }

      const lineTotal = lineTotalBeforeDiscount - discount;
      subtotal += lineTotal;

      return {
        lineNumber: index + 1,
        lineTotalBeforeDiscount,
        discount,
        lineTotal,
      };
    });

    // Apply package-level discount if specified
    let packageDiscount = 0;
    if (data.discountType && data.discountValue) {
      if (data.discountType === "PERCENTAGE") {
        packageDiscount = (subtotal * data.discountValue) / 100;
      } else {
        packageDiscount = data.discountValue;
      }
    }

    const totalAfterDiscount = subtotal - packageDiscount;
    const taxPercentage =
      data.taxPercentage || existingPackage.taxPercentage || 0;
    const totalTax = (totalAfterDiscount * taxPercentage) / 100;

    // Use manual price if override is enabled
    const grandTotal =
      data.manualPriceOverride && data.manualPrice
        ? data.manualPrice
        : totalAfterDiscount + totalTax;

    calculatedTotals = {
      subtotal,
      packageDiscount,
      discountType: data.discountType || existingPackage.discountType,
      discountValue: data.discountValue || existingPackage.discountValue,
      totalAfterDiscount,
      totalTax,
      grandTotal,
      taxPercentage,
      manualPriceOverride: data.manualPriceOverride || false,
      manualPrice: data.manualPriceOverride ? data.manualPrice : null,
    };
  }

  // Update package in a transaction
  const updatedPackage = await prisma.$transaction(async (tx) => {
    // Update the package
    const updatedPackageData = await tx.package.update({
      where: { id },
      data: {
        name: data.name?.trim() || existingPackage.name,
        description: data.description?.trim() || existingPackage.description,
        sku: data.sku?.trim() || existingPackage.sku,
        status: data.status || existingPackage.status,
        preferredVendor:
          data.preferredVendor?.trim() || existingPackage.preferredVendor,
        updatedById: userId,
        ...calculatedTotals,
      },
    });

    // Update lines if provided
    if (data.lines) {
      // Delete existing lines
      await tx.packageLine.deleteMany({
        where: { packageId: id },
      });

      // Create new lines
      const lineCalculations = data.lines.map((line, index) => {
        const lineTotalBeforeDiscount = line.quantity * line.unitPrice;
        let discount = 0;

        if (line.discountType === "PERCENTAGE") {
          discount =
            (lineTotalBeforeDiscount * (line.discountValue || 0)) / 100;
        } else {
          discount = line.discountValue || 0;
        }

        const lineTotal = lineTotalBeforeDiscount - discount;

        return {
          lineNumber: index + 1,
          lineTotalBeforeDiscount,
          discount,
          lineTotal,
        };
      });

      await Promise.all(
        data.lines.map((line, index) => {
          const calc = lineCalculations[index];
          return tx.packageLine.create({
            data: {
              lineNumber: calc.lineNumber,
              productId: line.productId || null,
              description: line.description.trim(),
              quantity: line.quantity,
              unitId: line.unitId || null,
              unitPrice: line.unitPrice,
              listPrice: line.listPrice || null,
              discount: calc.discount,
              discountType: line.discountType || "PERCENTAGE",
              discountValue: line.discountValue || 0,
              lineTotal: calc.lineTotal,
              lineTotalBeforeDiscount: calc.lineTotalBeforeDiscount,
              packageId: id,
              tenantId,
            },
          });
        }),
      );
    }

    return updatedPackageData;
  });

  return updatedPackage;
}

// Soft delete a package
export async function deletePackage(id: string) {
  const { tenantId, userId } = await getServerSession();

  if (!tenantId || !userId) {
    throw new Error("Unauthorized");
  }

  // Check if package exists and belongs to tenant
  const existingPackage = await prisma.package.findUnique({
    where: {
      id,
      tenantId,
      deleted: false,
    },
  });

  if (!existingPackage) {
    throw new Error("Package not found");
  }

  // Soft delete the package
  await prisma.package.update({
    where: { id },
    data: {
      deleted: true,
      deletedAt: new Date(),
      updatedById: userId,
    },
  });

  return { message: "Package deleted successfully" };
}
