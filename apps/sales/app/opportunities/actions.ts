"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import type { GetOpportunitiesSchema } from "./_lib/validations";
import { opportunitiesTableFields } from "shared-constant-table-fields";

export async function getOpportunities(input: GetOpportunitiesSchema) {
  // Get all permissions for opportunities model
  const permissions = await getModelPermissions("opportunity");

  // Check if user has permission to read opportunities
  if (!permissions.canView) {
    return {
      data: [],
      pageCount: 0,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      },
    };
  }

  const whereConditions = [
    // Always filter out soft-deleted opportunities
    { deleted: false },
    input.name
      ? { name: { contains: input.name, mode: "insensitive" } }
      : undefined,
    input.stage ? { stage: input.stage } : undefined,
    input.value && input.value.length === 2
      ? { value: { gte: input.value[0], lte: input.value[1] } }
      : undefined,
    input.probability && input.probability.length === 2
      ? {
          probability: { gte: input.probability[0], lte: input.probability[1] },
        }
      : undefined,
  ].filter(Boolean);

  // Use base query utility which should be enhanced to use RBAC-enabled Prisma client
  const data = await baseQuery(
    "opportunity",
    {
      ...input,
      tableFields: opportunitiesTableFields,
    },
    whereConditions
  );

  // Return both data and permissions for client-side use
  return {
    ...data,
    permissions,
  };
}

export async function getValueRange() {
  return { min: 0, max: 1000000 };
}

export async function getProbabilityRange() {
  return { min: 0, max: 100 };
}
