"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import toast from "react-hot-toast";
import { PlusIcon } from "lucide-react";
import type { OpportunityLineListResponse } from "@/types/opportunity-line";
import type { DataTableRowAction } from "@flinkk/data-table/types/data-table";
import { getOpportunityLinesColumns } from "./columns";
import { deleteOpportunityLine } from "./actions";
import { OpportunityLineSheet } from "./components/opportunity-line-sheet";
import { useCurrencyFormatterV2 } from "@flinkk/shared-hooks/use-currency-formatter-v2";
import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog
    ),
  {
    ssr: false,
  }
);

interface OpportunityLinesClientProps {
  opportunityId: string;
  initialData: OpportunityLineListResponse;
  userId: string;
}

export function OpportunityLinesClient({
  opportunityId,
  initialData,
  userId,
}: OpportunityLinesClientProps) {
  const router = useRouter();

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [editingLine, setEditingLine] = useState<any>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const [rowAction, setRowAction] = useState<DataTableRowAction<any> | null>(
    null
  );

  // Dynamic currency formatter
  const { formatCurrency, isLoading: currencyLoading } =
    useCurrencyFormatterV2();

  // Format currency with fallback
  const formatCurrencyWithFallback = (value: number) => {
    if (currencyLoading) {
      return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(value);
    }
    return formatCurrency(value);
  };

  const handleDeleteLine = async () => {
    if (rowAction?.type === "delete" && rowAction.row) {
      setIsDeleting(true);
      try {
        await deleteOpportunityLine(rowAction.row.id);
        toast.success("Opportunity line deleted successfully");
        setIsDeleteDialogOpen(false);
        setRowAction(null);
        router.refresh();
      } catch (error) {
        toast.error("Failed to delete opportunity line");
      } finally {
        setIsDeleting(false);
      }
    }
  };

  const handleEditLine = (line: any) => {
    setEditingLine(line);
    setIsSheetOpen(true);
  };

  const handleAddLine = () => {
    setEditingLine(null);
    setIsSheetOpen(true);
  };

  const handleSheetClose = () => {
    setIsSheetOpen(false);
    setEditingLine(null);
  };

  const handleSheetSuccess = () => {
    setIsSheetOpen(false);
    setEditingLine(null);
    router.refresh();
  };

  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setIsDeleteDialogOpen(true);
    } else if (rowAction?.type === "edit") {
      handleEditLine(rowAction.row);
      setRowAction(null);
    }
  }, [rowAction]);

  const columns = React.useMemo(
    () => getOpportunityLinesColumns({ setRowAction }),
    [setRowAction]
  );

  const { table } = useDataTable({
    data: initialData.lines,
    columns,
    pageCount: initialData.pagination.totalPages,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  // Calculate totals
  const totals = React.useMemo(() => {
    const lines = initialData.lines;
    const subtotal = lines.reduce((sum, line) => sum + line.totalPrice, 0);
    const totalDiscount = lines.reduce(
      (sum, line) =>
        sum + ((line.discount || 0) * line.quantity * line.unitPrice) / 100,
      0
    );
    const totalTax = lines.reduce(
      (sum, line) => sum + ((line.taxRate || 0) * line.totalPrice) / 100,
      0
    );
    const grandTotal = subtotal + totalTax;

    return {
      totalLines: lines.length,
      subtotal,
      totalDiscount,
      totalTax,
      grandTotal,
    };
  }, [initialData.lines]);

  return (
    <div className="space-y-4">
      {/* Summary Card */}
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold">
            Product Lines Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Lines:</span>
              <span className="font-medium">{totals.totalLines}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Subtotal:</span>
              <span className="font-medium">
                {formatCurrencyWithFallback(totals.subtotal)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Discount:</span>
              <span className="font-medium text-red-600">
                -{formatCurrencyWithFallback(totals.totalDiscount)}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Total Tax:</span>
              <span className="font-medium">
                {formatCurrencyWithFallback(totals.totalTax)}
              </span>
            </div>
            <div className="flex justify-between col-span-2 pt-2 border-t">
              <span className="font-semibold">Grand Total:</span>
              <span className="font-bold text-lg text-green-600">
                {formatCurrencyWithFallback(totals.grandTotal)}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card className="shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg font-semibold">
              Product Lines
            </CardTitle>
            <Button onClick={handleAddLine} size="sm">
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Product Line
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <DataTable table={table} doctype="opportunity-line">
            <DataTableToolbar
              table={table}
              buttonText="Add Product Line"
              onButtonClick={handleAddLine}
            />
          </DataTable>
        </CardContent>
      </Card>

      {/* Opportunity Line Sheet */}
      <OpportunityLineSheet
        isOpen={isSheetOpen}
        onClose={handleSheetClose}
        onSuccess={handleSheetSuccess}
        opportunityId={opportunityId}
        editingLine={editingLine}
      />

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && (
        <DeleteConfirmationDialog
          isOpen={isDeleteDialogOpen}
          onOpenChange={setIsDeleteDialogOpen}
          onDelete={handleDeleteLine}
          isLoading={isDeleting}
          title="Delete Product Line"
          description="Are you sure you want to delete this product line? This action cannot be undone."
        />
      )}
    </div>
  );
}
