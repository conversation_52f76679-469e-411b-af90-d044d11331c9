import * as React from "react";
import { OpportunitiesTable } from "./page-client";
import { getOpportunities } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";
import { checkInventoryConfigured } from "../../lib/check-inventory-configured";
import { getServerSession } from "next-auth";
import { authOptions, CustomSession } from "@flinkk/shared-auth/options";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";
import { NoAccessCard } from "@flinkk/edge-case/no-access-card";

export default async function ListPage(props: any) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters ?? []);

  // Get session to access tenantId
  const session = (await getServerSession(authOptions)) as CustomSession;

  // Check if inventory is configured for current tenant
  const hasInventoryConfigured = await checkInventoryConfigured(
    session?.tenantId || null
  );

  // Get opportunities data with permissions
  const result = await getOpportunities({
    ...search,
    filters: validFilters,
  });

  if (!Boolean(result?.permissions?.canView)) {
    return (
      <div className="p-4 h-full">
        <NoAccessCard />
      </div>
    );
  }

  if (
    result.data.length === 0 &&
    Object.keys(searchParams).length === 0
  ) {
    return (
      <div className="p-4 h-full">
        <NoDataCard
          title="No Opportunities Available"
          description="Opportunities will appear here once qualified leads are converted. Check your lead status or contact your team for updates."
        />
      </div>
    );
  }

  return (
    <div className="p-4 overflow-y-scroll h-full">
      <OpportunitiesTable
        data={result.data}
        pageCount={result.pageCount}
        permissions={result.permissions}
        hasInventoryConfigured={hasInventoryConfigured}
      />
    </div>
  );
}
