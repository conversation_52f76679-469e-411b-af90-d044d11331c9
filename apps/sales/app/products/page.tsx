import * as React from "react";
import { ProductsTable } from "./page-client";
import { getProducts } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";
import { NoAccessCard } from "@flinkk/edge-case/no-access-card";

export default async function ListPage(props: any) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters ?? []);

  // Get products data with permissions
  const result = await getProducts({
    ...search,
    filters: validFilters,
  });

  if (!Boolean(result?.permissions?.canView)) {
    return <NoAccessCard />;
  }

  if (result.data.length === 0 && Object.keys(searchParams).length === 0) {
    return (
      <NoDataCard
        title="No Products Found"
        description="Products represent the core offerings in your system. Add a new product to begin managing your catalog and quotations."
        href="/products/new"
        ctaName="Create Product"
      />
    );
  }

  return (
    <ProductsTable
      data={result.data}
      pageCount={result.pageCount}
      permissions={result.permissions}
    />
  );
}
