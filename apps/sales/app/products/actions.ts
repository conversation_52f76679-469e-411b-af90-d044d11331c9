"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import type { GetProductsSchema } from "./_lib/validations";
import { productsTableFields } from "shared-constant-table-fields";

export async function getProducts(input: GetProductsSchema) {
  // Get all permissions for products model
  const permissions = await getModelPermissions("Products");

  // Check if user has permission to read products
  if (!permissions.canView) {
    return {
      data: [],
      pageCount: 0,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      },
    };
  }
  const whereConditions = [
    input.name
      ? { name: { contains: input.name, mode: "insensitive" } }
      : undefined,
    input.sku
      ? { sku: { contains: input.sku, mode: "insensitive" } }
      : undefined,
    // Support both legacy price and new sellingPrice fields
    input.price && input.price.length === 2
      ? {
          OR: [
            { price: { gte: input.price[0], lte: input.price[1] } },
            { sellingPrice: { gte: input.price[0], lte: input.price[1] } },
          ],
        }
      : undefined,
    // Support both legacy cost and new costPrice fields
    input.cost && input.cost.length === 2
      ? {
          OR: [
            { cost: { gte: input.cost[0], lte: input.cost[1] } },
            { costPrice: { gte: input.cost[0], lte: input.cost[1] } },
          ],
        }
      : undefined,
  ].filter(Boolean);

  const data = await baseQuery(
    "product",
    {
      ...input,
      tableFields: productsTableFields,
      include: {
        unit: {
          select: {
            id: true,
            name: true,
            displayName: true,
          },
        },
        category: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    },
    whereConditions,
  );

  // Return both data and permissions for client-side use
  return {
    ...data,
    permissions,
  };
}

export async function getPriceRange() {
  return { min: 0, max: 10000 };
}

export async function getCostRange() {
  return { min: 0, max: 10000 };
}
