import * as React from "react";
import { LeadsTable } from "./page-client";
import { getLeads } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";
import { NoAccessCard } from "@flinkk/edge-case/no-access-card";

export default async function ListPage(props: any) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters ?? []);

  // Get leads data with permissions
  const result = await getLeads({
    ...search,
    filters: validFilters,
  });

  if (!Boolean(result?.permissions?.canView)) {
    return (
      <div className="p-4 h-full">
        <NoAccessCard />
      </div>
    );
  }

  if (result.data.length === 0 && Object.keys(searchParams).length === 0) {
    return (
      <div className="p-4 h-full">
        <NoDataCard
          title="Let’s Get Started"
          description="You don’t have any leads yet. Add your first lead to begin managing your sales pipeline."
          href="/leads/new"
          ctaName="Create Lead"
        />
      </div>
    );
  }

  return (
    <div className="p-4 overflow-y-scroll h-full">
      <LeadsTable
        data={result.data}
        pageCount={result.pageCount}
        permissions={result.permissions}
      />
    </div>
  );
}
