"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import type { GetLeadsSchema } from "./_lib/validations";
import { leadsTableFields } from "shared-constant-table-fields";

export async function getLeads(input: GetLeadsSchema) {
  try {
    // Get all permissions for leads model
    const permissions = await getModelPermissions("lead");

    // Check if user has permission to read leads
    if (!permissions.canView) {
      return {
        data: [],
        pageCount: 0,
        permissions: {
          canView: false,
          canCreate: false,
          canEdit: false,
          canDelete: false,
        },
      };
    }

    // Build where conditions
    const whereConditions = [
      input.firstName
        ? {
            OR: [
              { firstName: { contains: input.firstName, mode: "insensitive" } },
              { lastName: { contains: input.firstName, mode: "insensitive" } },
              { email: { contains: input.firstName, mode: "insensitive" } },
              { company: { contains: input.firstName, mode: "insensitive" } },
            ],
          }
        : undefined,
      input.stage ? { stage: input.stage } : undefined,
    ].filter(<PERSON><PERSON>an);

    // Use base query utility which handles field selection and related fields automatically
    // Note: baseQuery should be enhanced to use RBAC-enabled Prisma client
    const data = await baseQuery(
      "lead",
      {
        ...input,
        tableFields: leadsTableFields,
      },
      whereConditions
    );

    // Return both data and permissions for client-side use
    return {
      ...data,
      permissions,
    };
  } catch (error) {
    console.error("Error in getLeads:", error);
    throw error;
  }
}

export async function getValueRange() {
  return { min: 0, max: 100 };
}
