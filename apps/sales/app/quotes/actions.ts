"use server";

import { baseQuery } from "@flinkk/database/base-query";
import { getModelPermissions } from "@flinkk/shared-rbac";
import type { GetQuotesSchema } from "./_lib/validations";
import { quotesTableFields } from "shared-constant-table-fields";

export async function getQuotes(input: GetQuotesSchema) {
  // Get all permissions for quotes model
  const permissions = await getModelPermissions("Quotations");

  // Check if user has permission to read quotes
  if (!permissions.canView) {
    return {
      data: [],
      pageCount: 0,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      },
    };
  }

  // Ensure input has default values for required fields
  const safeInput = {
    page: 1,
    perPage: 10,
    sort: [{ id: "createdAt", desc: true }],
    filters: [],
    tableFields: [],
    joinOperator: "and" as const,
    name: "",
    status: "",
    createdAt: [],
    ...input,
  };

  const whereConditions = [
    safeInput.name
      ? { name: { contains: safeInput.name, mode: "insensitive" } }
      : undefined,
    safeInput.status ? { status: safeInput.status } : undefined,
  ].filter(Boolean);

  const data = await baseQuery(
    "quote",
    {
      ...safeInput,
      tableFields: quotesTableFields,
    },
    whereConditions
  );

  // Return both data and permissions for client-side use
  return {
    ...data,
    permissions,
  };
}
