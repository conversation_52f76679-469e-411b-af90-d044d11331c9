# Traveller Info Module

## Overview

The Traveller Info module is implemented as part of the Hotel Booking quotation flow. It collects structured data about the lead guest (Primary Contact) and any additional travellers involved in the booking.

## Implementation Details

### Components

1. **TravellerInfoForm** (`traveller-info-form.tsx`)

   - Main form component for collecting traveller information
   - <PERSON>les primary contact and additional travellers
   - Includes validation and age classification
   - Supports dynamic add/remove of travellers (max 10)

2. **useSaveTravellerInfo Hook** (`use-save-traveller-info.ts`)

   - Custom hook for saving traveller info to cart metadata
   - Includes comprehensive validation
   - Handles API communication with inventory system

3. **API Route** (`/api/hotel-management/cart/[cartId]/metadata`)
   - PATCH endpoint for updating cart metadata
   - Integrates with inventory system
   - Handles tenant-specific configuration

### Data Structure

The traveller information is stored in cart metadata as:

```json
{
  "metadata": {
    "traveller_info": {
      "primary_contact": {
        "name": "<PERSON>",
        "email": "<EMAIL>",
        "dob": "1990-03-12",
        "country": "Switzerland"
      },
      "travellers": [
        {
          "name": "<PERSON>",
          "dob": "2015-07-10"
        },
        {
          "name": "<PERSON>",
          "dob": "2018-12-25"
        }
      ]
    }
  }
}
```

### Validation Rules

- **Name**: Alphabetic only, no numbers or special characters
- **Email**: Standard email format (RFC 5322 compliant)
- **DOB**: Cannot be in future, must be a valid date
- **Country**: Optional, selected from predefined list
- **Co-Travellers**: Max 10, unique name+DOB combination per entry
- **Duplicates**: No duplicate name+DOB combinations allowed

### Age Classification

- **Infant**: 0-2 years
- **Child**: 3-12 years
- **Adult**: 13+ years

### Integration Points

1. **Form Schema**: Added `travellerInfo` field to `quoteFormSchema`
2. **Booking Flow**: Step 5 between Room Availability and Selected Room Bookings
3. **CRM Integration**: Pre-fills primary contact from opportunity data with priority order:
   - Opportunity contact (primary source)
   - Converted lead data (if opportunity was converted from lead)
   - Account contact information
   - Address parsing for country information
4. **Cart Metadata**: Saves to `cart.metadata.traveller_info`

### Usage

The module appears in the booking flow when:

- Hotel booking is enabled
- Room blocks have been selected
- User has completed previous steps (destination, hotel, dates, occupancy)

### Features

- ✅ Primary contact form with name, email, DOB (optional), country (optional)
- ✅ Additional travellers with add/remove functionality
- ✅ Real-time validation and error messages
- ✅ Age classification display
- ✅ Duplicate detection
- ✅ Enhanced CRM data pre-filling with multiple data sources
- ✅ Visual indicator for pre-filled data
- ✅ Cart metadata persistence
- ✅ Responsive design

### API Endpoints

- `PATCH /api/hotel-management/cart/[cartId]/metadata`
  - Updates cart metadata with traveller information
  - Requires tenant inventory configuration
  - Returns success/error response

### Error Handling

- Form validation errors displayed inline
- API errors shown via toast notifications
- Graceful fallback for missing inventory configuration
- Comprehensive logging for debugging

## Future Enhancements

- Integration with downstream modules (Itinerary, Invoice)
- Enhanced validation rules
- Support for passport information
- Meal preference collection
- Special requirements/accessibility needs
