"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@flinkk/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@flinkk/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@flinkk/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import { Input } from "@flinkk/components/ui/input";
import { Textarea } from "@flinkk/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Loader2, Receipt, Calendar } from "lucide-react";
import toast from "react-hot-toast";
import { toMinorUnits } from "@flinkk/money-math";

const recordPaymentSchema = z.object({
  payment_method: z.string().min(1, "Payment method is required"),
  reference_number: z.string().min(1, "Reference number is required"),
  collected_by: z.string().min(1, "Collected by is required"),
  collection_date: z.string().min(1, "Collection date is required"),
  amount_received: z
    .string()
    .min(1, "Amount received is required")
    .refine((val) => !isNaN(Number(val)) && Number(val) > 0, {
      message: "Amount must be a positive number",
    }),
  notes: z.string().optional(),
  bank_name: z.string().optional(),
  verification_status: z.string().optional(),
});

type RecordPaymentFormData = z.infer<typeof recordPaymentSchema>;

interface RecordPaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  quotationId: string;
  paymentCollectionId: string;
  currency?: string;
  expectedAmount?: number;
}

const PAYMENT_METHODS = [
  { value: "bank_transfer", label: "Bank Transfer" },
  { value: "cash", label: "Cash" },
  { value: "cheque", label: "Cheque" },
  { value: "card", label: "Card Payment" },
  { value: "online_transfer", label: "Online Transfer" },
  { value: "other", label: "Other" },
];

const VERIFICATION_STATUSES = [
  { value: "verified", label: "Verified" },
  { value: "pending", label: "Pending Verification" },
  { value: "requires_review", label: "Requires Review" },
];

export function RecordPaymentModal({
  isOpen,
  onClose,
  onSuccess,
  quotationId,
  paymentCollectionId,
  currency = "GBP",
  expectedAmount,
}: RecordPaymentModalProps) {
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<RecordPaymentFormData>({
    resolver: zodResolver(recordPaymentSchema),
    defaultValues: {
      payment_method: "",
      reference_number: "",
      collected_by: "",
      collection_date: new Date().toISOString().slice(0, 16), // Default to current datetime
      amount_received: expectedAmount ? expectedAmount.toString() : "",
      notes: "",
      bank_name: "",
      verification_status: "verified",
    },
  });

  const handleSubmit = async (data: RecordPaymentFormData) => {
    setIsLoading(true);
    try {
      const response = await fetch(
        `/api/quotation/${quotationId}/record-payment`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            payment_collection_id: paymentCollectionId,
            payment_method: data.payment_method,
            reference_number: data.reference_number,
            collected_by: data.collected_by,
            collection_date: new Date(data.collection_date).toISOString(),
            amount_received: toMinorUnits(
              Number(data.amount_received),
              currency,
            ),
            notes: data.notes,
            bank_name: data.bank_name,
            verification_status: data.verification_status,
          }),
        },
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to record payment");
      }

      const result = await response.json();

      if (result.success) {
        toast.success(
          `Payment of ${currency} ${data.amount_received} recorded successfully`,
        );
        onSuccess();
        form.reset();
        onClose();
      } else {
        throw new Error(result.message || "Failed to record payment");
      }
    } catch (error) {
      console.error("Error recording payment:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to record payment",
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      form.reset();
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Receipt className="h-5 w-5" />
            Record Advance Payment
          </DialogTitle>
          <DialogDescription>
            Record the manual payment details for the advance payment
            collection. This confirms that the customer has made the payment.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="space-y-4"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Payment Method */}
              <FormField
                control={form.control}
                name="payment_method"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Method *</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment method" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {PAYMENT_METHODS.map((method) => (
                          <SelectItem key={method.value} value={method.value}>
                            {method.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Amount Received */}
              <FormField
                control={form.control}
                name="amount_received"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Amount Received ({currency}) *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">
                          {currency}
                        </span>
                        <Input
                          {...field}
                          type="number"
                          step="0.01"
                          min="0.01"
                          placeholder="0.00"
                          className="pl-12"
                          disabled={isLoading}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Reference Number */}
              <FormField
                control={form.control}
                name="reference_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Reference Number *</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Transaction ID, Receipt #, etc."
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Collected By */}
              <FormField
                control={form.control}
                name="collected_by"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Collected By *</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="sales_team, agent name, etc."
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Collection Date */}
              <FormField
                control={form.control}
                name="collection_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Collection Date *</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                        <Input
                          {...field}
                          type="datetime-local"
                          className="pl-10"
                          disabled={isLoading}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Verification Status */}
              <FormField
                control={form.control}
                name="verification_status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Verification Status</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {VERIFICATION_STATUSES.map((status) => (
                          <SelectItem key={status.value} value={status.value}>
                            {status.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Bank Name */}
            <FormField
              control={form.control}
              name="bank_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Bank Name</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      placeholder="Customer's bank name (optional)"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Additional notes about the payment (optional)"
                      rows={3}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isLoading ? "Recording..." : "Record Payment"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
