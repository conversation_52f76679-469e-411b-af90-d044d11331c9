"use client";

import React from "react";
import { Button } from "@flinkk/components/ui/button";
import { Input } from "@flinkk/components/ui/input";
import { Label } from "@flinkk/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@flinkk/components/ui/table";
import { PlusIcon, TrashIcon, PackageIcon } from "lucide-react";
import { Badge } from "@flinkk/components/ui/badge";
import { formatCurrency } from "@flinkk/money-math";
// import {
//   Select,
//   SelectContent,
//   SelectItem,
//   SelectTrigger,
//   SelectValue,
// } from "@flinkk/components/ui/select";
import { ProductAutocomplete } from "./product-autocomplete";
import { ProductPriceBookAutocomplete } from "./product-price-book-autocomplete";
import toast from "react-hot-toast";

interface LineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  lineTotal: number;
  productId?: string;
  priceBookId?: string;
  productSku?: string;
  productDescription?: string;
  isPrePopulated?: boolean;
}

interface LineItemsTableProps {
  lineItems: LineItem[];
  setLineItems: (items: LineItem[]) => void;
  onCalculateTotal: () => void;
  currency?: string; // Add currency prop
}

export function LineItemsTable({
  lineItems,
  setLineItems,
  onCalculateTotal,
  currency = "USD", // Default to USD
}: LineItemsTableProps) {
  // Add a new line item
  const addLineItem = () => {
    const newItem: LineItem = {
      id: crypto.randomUUID(),
      description: "",
      quantity: 1,
      unitPrice: 0,
      lineTotal: 0,
      productId: "",
      priceBookId: "",
    };
    setLineItems([...lineItems, newItem]);
  };

  // Remove a line item
  const removeLineItem = (id: string) => {
    if (lineItems.length > 1) {
      setLineItems(lineItems.filter((item) => item.id !== id));
      // Trigger recalculation after state update
      setTimeout(onCalculateTotal, 0);
    }
  };

  // Update a line item field
  const updateLineItem = (id: string, field: keyof LineItem, value: any) => {
    const updatedItems = lineItems.map((item) => {
      if (item.id === id) {
        const updatedItem = { ...item, [field]: value };

        // Recalculate line total when quantity or unit price changes
        if (field === "quantity" || field === "unitPrice") {
          updatedItem.lineTotal = updatedItem.quantity * updatedItem.unitPrice;
        }

        return updatedItem;
      }
      return item;
    });

    setLineItems(updatedItems);
    // Trigger recalculation after state update
    setTimeout(onCalculateTotal, 0);
  };

  // Generate SKU from product name
  const generateSKU = (productName: string): string => {
    // Remove special characters and convert to uppercase
    const cleanName = productName
      .replace(/[^a-zA-Z0-9\s]/g, "")
      .toUpperCase()
      .trim();

    // Take first 3 words and limit to 8 characters
    const words = cleanName.split(" ").slice(0, 3);
    const sku = words
      .map((word) => word.slice(0, 3))
      .join("")
      .slice(0, 8);

    return sku || "PROD";
  };

  // Format currency amount using money-math
  const formatCurrencyAmount = (amount: number) => {
    // Convert to minor units for formatting (assuming amounts are in major units)
    return formatCurrency(amount * 100, currency);
  };

  // Create product from line item
  const createProductFromLineItem = async (lineItem: LineItem) => {
    try {
      // Validate required fields
      if (!lineItem.description.trim()) {
        toast.error("Please enter a product description first");
        return;
      }

      if (lineItem.unitPrice <= 0) {
        toast.error("Please enter a valid unit price");
        return;
      }

      // Prepare product data
      const productData = {
        name: lineItem.description,
        sku: lineItem.productSku || generateSKU(lineItem.description),
        description: lineItem.productDescription || lineItem.description,
        sellingPrice: lineItem.unitPrice,
        costPrice: lineItem.unitPrice * 0.7, // Assume 30% markup
        type: "PRODUCT",
        status: "ACTIVE",
      };

      // Create product via API
      const response = await fetch("/api/products", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(productData),
      });

      if (!response.ok) {
        throw new Error("Failed to create product");
      }

      const newProduct = await response.json();

      // Update line item with new product ID
      updateLineItem(lineItem.id, "productId", newProduct.id);
      updateLineItem(lineItem.id, "productSku", newProduct.sku);

      toast.success(`Product "${newProduct.name}" created successfully!`);
    } catch (error) {
      console.error("Error creating product:", error);
      toast.error("Failed to create product. Please try again.");
    }
  };

  // Handle product selection
  const handleProductSelect = (id: string, product: any) => {
    updateLineItem(id, "productId", product.id);
    updateLineItem(id, "description", product.name);
    updateLineItem(id, "productSku", product.sku);
    updateLineItem(id, "productDescription", product.description);

    // Set unit price if available
    if (product.sellingPrice !== undefined) {
      updateLineItem(id, "unitPrice", product.sellingPrice);
    } else if (product.price !== undefined) {
      updateLineItem(id, "unitPrice", product.price);
    }
  };

  // Handle price book selection
  const handlePriceBookSelect = (id: string, priceBookData: any) => {
    updateLineItem(id, "priceBookId", priceBookData.id);
    updateLineItem(id, "unitPrice", priceBookData.price);
  };

  // Check if product can be created from line item
  const canCreateProduct = (item: LineItem): boolean => {
    return (
      item.description.trim().length > 0 &&
      item.unitPrice > 0 &&
      !item.productId // Only if no product is already selected
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Line Items</h3>
          <p className="text-sm text-gray-500">
            Add products or services to your quote
          </p>
        </div>
        <Button
          type="button"
          onClick={addLineItem}
          className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
        >
          <PlusIcon className="mr-2 h-4 w-4" />
          Add Item
        </Button>
      </div>

      <div className="border border-gray-200 rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead className="px-4 py-3 text-left font-medium text-gray-700">
                Description
              </TableHead>
              <TableHead className="px-4 py-3 text-center font-medium text-gray-700">
                Quantity
              </TableHead>
              <TableHead className="px-4 py-3 text-right font-medium text-gray-700">
                Unit Price
              </TableHead>
              <TableHead className="px-4 py-3 text-right font-medium text-gray-700">
                Line Total
              </TableHead>
              <TableHead className="px-4 py-3 text-center font-medium text-gray-700">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {lineItems.map((item, index) => (
              <TableRow key={item.id} className="hover:bg-gray-50">
                <TableCell className="px-4 py-4">
                  <div className="space-y-2">
                    <Input
                      type="text"
                      value={item.description}
                      onChange={(e) =>
                        updateLineItem(item.id, "description", e.target.value)
                      }
                      placeholder="Product or service description"
                      className="border border-gray-200 rounded-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 p-3 min-h-[44px] font-medium bg-white"
                    />
                    <div className="flex items-center gap-2">
                      <ProductAutocomplete
                        value={item.description}
                        onChange={(value) =>
                          updateLineItem(item.id, "description", value)
                        }
                        onProductSelect={(product) =>
                          handleProductSelect(item.id, product)
                        }
                        placeholder="Search products..."
                        className="flex-1"
                      />
                      <ProductPriceBookAutocomplete
                        value={item.description}
                        productId={item.productId}
                        priceBookId={item.priceBookId}
                        onChange={(value) =>
                          updateLineItem(item.id, "description", value)
                        }
                        onProductSelect={(product) =>
                          handleProductSelect(item.id, product)
                        }
                        onPriceBookSelect={(priceBook) =>
                          handlePriceBookSelect(item.id, priceBook)
                        }
                        placeholder="Price book..."
                        className="w-32"
                      />
                    </div>
                    {item.isPrePopulated && (
                      <div className="flex items-center gap-2 text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded-md w-fit">
                        <PackageIcon className="h-3 w-3" />
                        <span className="font-medium">From Opportunity</span>
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell className="px-4 py-4">
                  <Input
                    type="number"
                    min="1"
                    step="1"
                    value={item.quantity}
                    onChange={(e) =>
                      updateLineItem(
                        item.id,
                        "quantity",
                        parseInt(e.target.value) || 1,
                      )
                    }
                    className="border border-gray-200 rounded-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 p-3 text-center min-h-[44px] font-medium bg-white"
                  />
                </TableCell>
                <TableCell className="px-4 py-4">
                  <Input
                    type="number"
                    min="0"
                    step="0.01"
                    value={item.unitPrice}
                    onChange={(e) =>
                      updateLineItem(
                        item.id,
                        "unitPrice",
                        parseFloat(e.target.value) || 0,
                      )
                    }
                    className="border border-gray-200 rounded-lg shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 p-3 text-right min-h-[44px] font-medium bg-white"
                    placeholder="0.00"
                  />
                </TableCell>
                <TableCell className="px-4 py-4 text-right">
                  <div className="font-semibold text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-lg">
                    {formatCurrencyAmount(item.lineTotal)}
                  </div>
                </TableCell>
                <TableCell className="px-4 py-4">
                  <div className="flex items-center justify-center gap-2">
                    {canCreateProduct(item) && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => createProductFromLineItem(item)}
                        className="h-9 px-3 text-green-600 border-green-200 hover:bg-green-50 hover:border-green-300 transition-colors shadow-sm"
                        title="Create product from this line item"
                      >
                        <PackageIcon className="h-3 w-3 mr-1" />
                        Create
                      </Button>
                    )}
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => removeLineItem(item.id)}
                      disabled={lineItems.length === 1}
                      className="h-9 w-9 p-0 text-red-500 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      title="Remove line item"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {lineItems.length === 0 && (
        <div className="text-center py-12 bg-gray-50/50 rounded-xl border border-gray-200">
          <div className="max-w-sm mx-auto">
            <PackageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              No line items yet
            </h3>
            <p className="text-sm text-gray-500 mb-6">
              Add products or services to get started with your quote.
            </p>
            <Button
              type="button"
              variant="default"
              onClick={addLineItem}
              className="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
            >
              <PlusIcon className="mr-2 h-4 w-4" />
              Add First Item
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
