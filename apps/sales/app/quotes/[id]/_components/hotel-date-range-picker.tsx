"use client";

import React from "react";
import { DateFieldFormElement } from "@flinkk/dynamic-form/form-elements";
import { Control } from "react-hook-form";

interface HotelDateRangePickerProps {
  control: Control<any>;
  className?: string;
}

export function HotelDateRangePicker({
  control,
  className = "",
}: HotelDateRangePickerProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <DateFieldFormElement
        name="checkInDate"
        label="Check-in Date"
        control={control}
        required={true}
        helperText="Select your arrival date"
      />
      <DateFieldFormElement
        name="checkOutDate"
        label="Check-out Date"
        control={control}
        required={true}
        helperText="Select your departure date"
      />
    </div>
  );
}
