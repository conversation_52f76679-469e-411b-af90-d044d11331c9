"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { useSaveFormData } from "@flinkk/hooks/mutation/use-save-form-data";
import { Button } from "@flinkk/components/ui/button";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from "@flinkk/components/ui/form";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@flinkk/components/ui/tooltip";
import {
  Loader2Icon,
  CreditCard,
  Receipt,
  CheckCircle,
  Calendar,
  PlusIcon,
} from "lucide-react";
import { EnhancedQuoteGeneratorButton } from "@flinkk/quote-pdf";
import { useQuoteTemplate } from "@flinkk/shared-hooks/use-quote-template";
import { useQuery } from "@tanstack/react-query";
import {
  formatCurrency as formatCurrencyMath,
  toMinorUnits,
} from "@flinkk/money-math";

import {
  TextFieldFormElement,
  TextAreaFieldFormElement,
  DynamicSelectFieldFormElement,
  NumberFieldFormElement,
} from "@flinkk/dynamic-form/form-elements";
import {
  HotelBookingCalendar,
  RoomBlock,
} from "./_components/hotel-booking-calendar";
import { SavedRoomBookings } from "./_components/cart-items-display";
import { useSaveRoomBookings } from "./_hooks/use-save-room-bookings";
import { useSaveAddons } from "./_hooks/use-save-addons";
import {
  QuotationStatusSwitch,
  QuotationStatus,
} from "./_components/quotation-status-switch";
import { AdvancePaymentModal } from "./_components/advance-payment-modal";
import { RecordPaymentModal } from "./_components/record-payment-modal";
import { toast } from "sonner";

// Country configuration hook
function useCurrentCountryConfiguration() {
  return useQuery({
    queryKey: ["current-country-configuration"],
    queryFn: async () => {
      const response = await fetch("/api/country-configurations/current");
      if (!response.ok) {
        throw new Error("Failed to fetch country configuration");
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Organization data hook
function useOrganizationData() {
  return useQuery({
    queryKey: ["organization-data"],
    queryFn: async () => {
      const response = await fetch("/api/quote-templates");
      if (!response.ok) {
        throw new Error("FailshouldConvertCarted to fetch organization data");
      }
      return response.json();
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Currency formatter hook using @flinkk/money-math
function useCurrencyFormatter() {
  const { data: countryConfig, isLoading } = useCurrentCountryConfiguration();

  const formatCurrency = (amount: number) => {
    // Get currency from country config or default to USD
    const currency = countryConfig?.configuration?.currencyCode || "USD";

    // Convert amount to minor units and format using money-math
    const minorUnits = toMinorUnits(amount, currency);
    return formatCurrencyMath(minorUnits, currency);
  };

  return {
    formatCurrency,
    isLoading,
    countryConfig: countryConfig?.configuration,
  };
}

// Cart currency formatter hook using @flinkk/money-math
function useCartCurrencyFormatter(cartDetails: any) {
  // Extract currency from cart details
  const cartCurrency = cartDetails?.currency_code || "USD";

  const formatCartCurrency = (amount: number) => {
    // Convert amount to minor units and format using money-math
    const minorUnits = toMinorUnits(amount, cartCurrency);
    return formatCurrencyMath(minorUnits, cartCurrency);
  };

  return {
    formatCartCurrency,
    cartCurrency,
    cartCurrencySymbol: "", // Not needed with money-math
    isLoading: false, // No loading state for cart currency
  };
}

// Import section wrapper
import { SectionWrapper } from "@flinkk/dynamic-form/components";

// Import the enhanced line items table component
import { EnhancedLineItemsTable } from "../../packages/_components/enhanced-line-items-table";
// Import the package line items table component
import { PackageLineItemsTable } from "../_components/package-line-items-table";
// Import hotel booking components
import { HotelDateRangePicker } from "./_components/hotel-date-range-picker";
import { GuestDetailsForm } from "./_components/guest-details-form";
import { validateGuestCapacityForHotel } from "./_components/room-availability-grid";
import { TravellerInfoForm } from "./_components/traveller-info-form";
import { useSaveTravellerInfo } from "./_hooks/use-save-traveller-info";

// Quote form schema
const quoteFormSchema = z.object({
  quoteNumber: z.string().default(""), // Auto-generated quote number
  description: z.string().default(""),
  status: z.string().default("DRAFT"),

  // Customer/Company selection
  contactId: z.string().default(""),
  accountId: z.string().default(""),
  opportunityId: z.string().default(""),

  // Hotel booking (for inventory-enabled organizations)
  selectedDestination: z.string().default(""),
  selectedHotel: z.string().default(""),
  checkInDate: z.string().default(""),
  checkOutDate: z.string().default(""),
  adults: z.number().min(1, "At least 1 adult is required").default(1),
  children: z.number().min(0, "Children cannot be negative").default(0),

  // Traveller Info (for inventory-enabled organizations)
  travellerInfo: z
    .object({
      primary_contact: z.object({
        name: z.string().default(""),
        email: z.string().default(""),
        dob: z.string().optional(),
        country: z.string().optional(),
      }),
      travellers: z
        .array(
          z.object({
            name: z.string().default(""),
            dob: z.string().default(""),
          }),
        )
        .default([]),
    })
    .default({
      primary_contact: { name: "", email: "", dob: "", country: "" },
      travellers: [],
    }),

  // Add-ons (for inventory-enabled organizations)
  addonCategory: z.string().default(""),

  // Dates
  validUntil: z.string().default(""),

  // Terms
  paymentTerms: z.string().default("Net 30"),
  deliveryTerms: z.string().default(""),

  // Quotation From fields
  quotationFromCountry: z.string().min(1, "Country is required"),
  quotationFromBusinessName: z.string().min(1, "Business name is required"),
  quotationFromGSTIN: z.string().default(""),
  quotationFromAddress: z.string().default(""),
  quotationFromCity: z.string().default(""),
  quotationFromPostalCode: z.string().default(""),
  quotationFromState: z.string().default(""),

  // Quotation To fields (existing)
  quotationToCountry: z.string().default(""),
  quotationToBusinessName: z.string().default(""),
  quotationToGSTIN: z.string().default(""),
  quotationToAddress: z.string().default(""),
  quotationToCity: z.string().default(""),
  quotationToPostalCode: z.string().default(""),
  quotationToState: z.string().default(""),

  // Pricing (will be calculated from line items)
  subtotal: z.number().default(0),
  totalTax: z.number().default(0),
  totalDiscount: z.number().default(0),
  grandTotal: z.number().default(0),

  // Enhanced pricing fields
  quoteDiscountType: z.string().default("PERCENTAGE"),
  quoteDiscountValue: z.coerce
    .number()
    .min(0, "Discount value must be positive")
    .default(0),
  intraStateTaxRate: z.coerce
    .number()
    .min(0, "Tax rate must be positive")
    .max(100, "Tax rate cannot exceed 100%")
    .default(0),
  interStateTaxRate: z.coerce
    .number()
    .min(0, "Tax rate must be positive")
    .max(100, "Tax rate cannot exceed 100%")
    .default(0),
  adjustments: z.coerce.number().default(0),
  isManualTotal: z.boolean().default(false),
  manualTotalValue: z.coerce.number().optional(),

  // Manual grand total override
  isManualGrandTotal: z.boolean().default(false),
  manualGrandTotal: z.coerce.number().optional(),

  // Tax settings
  taxPercentage: z.number().default(0),

  // Line items
  lineItems: z
    .array(
      z.object({
        id: z.string(),
        description: z.string(),
        quantity: z.number().min(1, "Quantity must be at least 1"),
        unitPrice: z.number().min(0, "Unit price must be positive"),
        lineTotal: z.number().default(0),
        productId: z.string().default(""),
        unitType: z.string().optional(),
        discountType: z.string().default("PERCENTAGE"),
        discountValue: z.number().default(0),
        taxRate: z.number().default(0),
        subtotal: z.number().default(0),
      }),
    )
    .default([]),

  // Hotel room blocks (not part of form validation, managed separately)
  hotelRoomBlocks: z.array(z.any()).optional(),
});

type QuoteFormValues = z.infer<typeof quoteFormSchema>;

interface QuoteCreateFormProps {
  id: string;
  initialData?: any;
  opportunityId?: string;
  opportunityData?: any;
  opportunityProducts?: any[];
  opportunityPackages?: any[];
  organizationDetails?: any;
  inventoryConfig?: any;
  existingCartData?: RoomBlock[];
  existingAddons?: any[];
  cartDetails?: any; // Complete cart object from inventory API
  mode?: string;
}

// Helper function to safely parse number values
const safeParseNumber = (value: any, defaultValue: number = 0): number => {
  if (value === null || value === undefined || value === "") {
    return defaultValue;
  }
  const parsed = parseFloat(value);
  return isNaN(parsed) ? defaultValue : parsed;
};

export function QuoteCreateForm({
  id,
  initialData,
  opportunityId,
  opportunityData,
  opportunityProducts = [],
  opportunityPackages = [],
  organizationDetails,
  inventoryConfig,
  existingCartData = [],
  existingAddons = [],
  cartDetails = null,
  mode = "edit",
}: QuoteCreateFormProps) {
  console.log({ existingCartData });
  console.log({ cartDetails });

  // Log cart summary for debugging
  if (cartDetails) {
    console.log("🛒 Cart loaded:", {
      id: cartDetails.id,
      email: cartDetails.email,
      total: cartDetails.total,
      itemCount: cartDetails.items?.length || 0,
    });
  }

  // Utility functions for accessing cart item details (available for future use)
  // const getCartItemById = (itemId: string) => {
  //   return cartDetails?.items?.find((item: any) => item.id === itemId);
  // };

  // const getCartItemMetadata = (itemId: string) => {
  //   const item = getCartItemById(itemId);
  //   return item?.metadata || {};
  // };

  // const getAllCartItems = () => {
  //   return cartDetails?.items || [];
  // };

  // const getCartSummary = () => {
  //   return {
  //     id: cartDetails?.id,
  //     email: cartDetails?.email,
  //     customerId: cartDetails?.customer_id,
  //     regionId: cartDetails?.region_id,
  //     currencyCode: cartDetails?.currency_code,
  //     total: cartDetails?.total,
  //     subtotal: cartDetails?.subtotal,
  //     taxTotal: cartDetails?.tax_total,
  //     itemCount: cartDetails?.items?.length || 0,
  //   };
  // };
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  const isEditMode = id !== "new" && initialData;
  const isViewMode = mode === "view" && isEditMode;

  console.log({ initialData });

  // State for selected destination and hotel data
  const [selectedDestinationData, setSelectedDestinationData] =
    useState<any>(null);
  const [selectedHotelData, setSelectedHotelData] = useState<any>(null);

  // State for hotel room blocks - only for newly selected bookings (not existing cart data)
  const [hotelRoomBlocks, setHotelRoomBlocks] = useState<RoomBlock[]>([]);

  // State for guest capacity validation
  const [capacityValidation, setCapacityValidation] = useState<{
    errors: string[];
    warnings: string[];
  }>({ errors: [], warnings: [] });

  // Log the existing cart data for debugging
  useEffect(() => {
    if (existingCartData.length > 0) {
      console.log("🏨 Existing cart data available:", existingCartData);
      console.log("🏨 Hotel room blocks (new selections):", hotelRoomBlocks);
    }
  }, [existingCartData, hotelRoomBlocks]);

  // State for add-ons
  const [addons, setAddons] = useState<any[]>([]);
  const [showAddonForm, setShowAddonForm] = useState(false);
  const [addonForm, setAddonForm] = useState({
    category: "",
    productService: "",
    startDate: "",
    endDate: "",
    price: "",
  });
  const [addonProducts, setAddonProducts] = useState<any[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(false);
  const [isLoadingOfferings, setIsLoadingOfferings] = useState(false);

  // State for advance payment modal
  const [isAdvancePaymentModalOpen, setIsAdvancePaymentModalOpen] =
    useState(false);

  // State for record payment modal
  const [isRecordPaymentModalOpen, setIsRecordPaymentModalOpen] =
    useState(false);

  // Handler for advance payment success
  const handleAdvancePaymentSuccess = (paymentCollectionId: string) => {
    // Refresh the page data or update the state to reflect the payment collection ID
    toast.success("Advance payment initiated successfully");
    // You could also trigger a refetch of the quote data here
    window.location.reload(); // Simple approach to refresh the data
  };

  // Handler for record payment success
  const handleRecordPaymentSuccess = () => {
    // Refresh the page data or update the state to reflect the payment recording
    toast.success("Payment recorded successfully");
    // You could also trigger a refetch of the quote data here
    window.location.reload(); // Simple approach to refresh the data
  };

  // Initialize selected destination and hotel data from existing quote
  useEffect(() => {
    if (initialData?.selectedDestinationData) {
      setSelectedDestinationData(initialData.selectedDestinationData);
    }
    if (initialData?.selectedHotelData) {
      setSelectedHotelData(initialData.selectedHotelData);
    }

    // Initialize addons from either saved quote data or existing cart data
    if (initialData?.addons) {
      setAddons(initialData.addons);
    } else if (existingAddons && existingAddons.length > 0) {
      console.log("🎯 Initializing addons from cart data:", existingAddons);
      setAddons(existingAddons);
    }
  }, [initialData, existingAddons]);

  // Fetch organization's template preference
  const {
    templateId,
    templateName,
    isLoading: isTemplateLoading,
    error: templateError,
  } = useQuoteTemplate();

  // Country configuration hooks
  const { data: countryConfig } = useCurrentCountryConfiguration();
  const { formatCurrency } = useCurrencyFormatter();

  // Cart currency formatter - uses cart currency when available
  const { formatCartCurrency, cartCurrency, cartCurrencySymbol } =
    useCartCurrencyFormatter(cartDetails);

  // Use cart currency formatter when cart details are available, otherwise fall back to country config
  const activeFormatCurrency = cartDetails
    ? formatCartCurrency
    : formatCurrency;
  const activeCurrencySymbol = cartDetails
    ? cartCurrencySymbol
    : countryConfig?.configuration?.currencySymbol || "$";

  // Organization data hooks
  const { data: organizationData } = useOrganizationData();

  // Traveller info hook
  const { saveTravellerInfo, isSaving: isSavingTravellerInfo } =
    useSaveTravellerInfo();

  // Room bookings save hook
  const { saveRoomBookings, isLoading: isSavingRoomBookings } =
    useSaveRoomBookings();

  // Addons save hook
  const { saveAddons, isLoading: isSavingAddons } = useSaveAddons();

  // Helper function to determine if sections should be shown
  // If no inventory config provided, default to showing sections (fail-safe approach)
  const shouldShowInventorySections = () => {
    if (!inventoryConfig) {
      return true; // Show sections if no config provided
    }
    return !inventoryConfig.isConfigured;
  };

  // State to control visibility of hotel selection and calendar
  const [showAvailabilityCheck, setShowAvailabilityCheck] = useState(false);

  // Helper function to check if there are saved room bookings in cart
  const hasSavedRoomBookings = () => {
    if (!cartDetails || !cartDetails.items) return false;
    return cartDetails.items.some(
      (item) => item.metadata?.hotel_name || item.metadata?.room_config_name,
    );
  };

  // Handler for Check Availability button
  const handleCheckAvailability = () => {
    // Extract hotel information from saved bookings
    const hotelItem = cartDetails?.items?.find(
      (item) => item.metadata?.hotel_name || item.metadata?.hotel_id,
    );

    if (hotelItem?.metadata?.hotel_id) {
      // Set the hotel in the form to trigger availability check
      form.setValue("selectedHotel", hotelItem.metadata.hotel_id);
      // Also set the hotel data for the calendar component
      setSelectedHotelData({
        value: hotelItem.metadata.hotel_id,
        label: hotelItem.metadata.hotel_name || "Hotel",
        name: hotelItem.metadata.hotel_name || "Hotel",
      });
    }

    // Clear any existing new selections to start fresh
    setHotelRoomBlocks([]);

    // Show the availability check interface
    setShowAvailabilityCheck(true);
  };

  // Helper functions for add-ons
  const handleCategoryChange = async (categoryId: string) => {
    setAddonForm((prev) => ({
      ...prev,
      category: categoryId,
      productService: "",
      price: "",
    }));
    setAddonProducts([]);

    if (categoryId) {
      setIsLoadingProducts(true);
      try {
        const response = await fetch("/api/dynamic-select/addon-products", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ categoryId }),
        });
        const products = await response.json();
        setAddonProducts(products);
      } catch (error) {
        console.error("Error fetching products:", error);
      } finally {
        setIsLoadingProducts(false);
      }
    }
  };

  const handleFindOfferings = async () => {
    if (!addonForm.productService || !addonForm.startDate || !addonForm.endDate)
      return;

    setIsLoadingOfferings(true);
    try {
      const response = await fetch("/api/addon-offerings", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          productServiceId: addonForm.productService,
          startDate: addonForm.startDate,
          endDate: addonForm.endDate,
        }),
      });
      const result = await response.json();

      if (result.success && result.highestPrice > 0) {
        setAddonForm((prev) => ({
          ...prev,
          price: result.highestPrice.toString(),
        }));
      }
    } catch (error) {
      console.error("Error fetching offerings:", error);
    } finally {
      setIsLoadingOfferings(false);
    }
  };

  const handleAddAddon = async () => {
    if (
      !addonForm.category ||
      !addonForm.productService ||
      !addonForm.startDate ||
      !addonForm.endDate ||
      !addonForm.price
    ) {
      return;
    }

    const selectedProduct = addonProducts.find(
      (p) => p.value === addonForm.productService,
    );
    const newAddon = {
      id: crypto.randomUUID(),
      categoryId: addonForm.category,
      productServiceId: addonForm.productService,
      productServiceName: selectedProduct?.label || "",
      startDate: addonForm.startDate,
      endDate: addonForm.endDate,
      price: parseFloat(addonForm.price),
      quantity: 1,
      total: parseFloat(addonForm.price),
      // Include API-provided product identifiers for proper inventory integration
      productId: selectedProduct?.product_id,
      productVariantId: selectedProduct?.product_variant_id,
    };

    // Add to local state first
    setAddons((prev) => [...prev, newAddon]);

    // If we have a cart ID, save to cart immediately
    if (initialData?.inventoryCartId) {
      try {
        await saveAddons({
          addons: [newAddon], // Save only the new addon
          cartId: initialData.inventoryCartId,
        });
        console.log("✅ Addon saved to cart successfully");
      } catch (error) {
        console.error("❌ Error saving addon to cart:", error);
        // Don't remove from local state on error - user can retry later
      }
    } else {
      console.log("ℹ️ No cart ID available, addon saved locally only");
    }

    // Reset form
    setAddonForm({
      category: "",
      productService: "",
      startDate: "",
      endDate: "",
      price: "",
    });
    setAddonProducts([]);
    setShowAddonForm(false);
  };

  // Handle status change with cart conversion
  const handleStatusChange = async (newStatus: QuotationStatus) => {
    if (!isEditMode || !initialData) {
      toast.error("Cannot update status for new quotations");
      return;
    }

    // Show loading toast for status updates
    const loadingToast = toast.loading(`Updating status to ${newStatus}...`);

    try {
      // Prepare the request payload
      const payload: any = {
        quotationId: initialData.id,
        status: newStatus,
      };

      // Call the status update API
      const response = await fetch("/api/method/quotation/status-update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });

      const result = await response.json();

      if (!response.ok) {
        const errorMessage = result.error || "Failed to update status";
        const errorDetails = result.details ? ` (${result.details})` : "";
        throw new Error(errorMessage + errorDetails);
      }

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Update the form status
      form.setValue("status", newStatus);

      // (Cart conversion logic removed)
    } catch (error) {
      // Dismiss any loading toasts
      toast.dismiss(loadingToast);

      console.error("Status update error:", error);

      let errorMessage = "Failed to update status";
      if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast.error(errorMessage, { duration: 5000 });
    }
  };

  // Handle saving room bookings to cart
  const handleAddRoomBookingsToQuotation = async (
    roomBlocks: RoomBlock[],
    hotelData?: any,
  ) => {
    console.log("🎯 Save room booking button clicked!", {
      roomBlocks,
      hotelData,
    });

    console.log("Hello");

    // For new quotes, we need to save the quote first to get a cart ID
    if (id === "new") {
      console.error(
        "Cannot save room bookings for unsaved quote. Please save the quote first.",
      );
      if (typeof window !== "undefined") {
        const { toast } = await import("sonner");
        toast.error(
          "❌ Please save the quote first before adding room bookings",
        );
      }
      return;
    }

    // Get the cart ID from the current quote
    const cartId = initialData?.inventoryCartId;

    if (!cartId) {
      console.error("No cart ID found for this quote");
      if (typeof window !== "undefined") {
        const { toast } = await import("sonner");
        toast.error("❌ No cart found for this quote. Please contact support.");
      }
      return;
    }

    console.log({ hotelData });

    try {
      // Get hotel name from selectedHotelData (from the dropdown selection)
      // Priority: selectedHotelData.label > selectedHotelData.name > hotelData.availabilityData.hotel_name > fallback
      const hotelName =
        selectedHotelData?.label ||
        selectedHotelData?.name ||
        selectedHotelData?.title ||
        hotelData?.availabilityData?.hotel_name ||
        hotelData?.name ||
        "Unknown Hotel";

      // Get destination information from form state
      const destinationId = form.watch("selectedDestination");
      const destinationName =
        selectedDestinationData?.label ||
        selectedDestinationData?.name ||
        selectedDestinationData?.title;
      const checkInDate = form.watch("checkInDate");
      const checkOutDate = form.watch("checkOutDate");

      const result = await saveRoomBookings({
        roomBlocks,
        cartId,
        hotelId: hotelData?.selectedHotel,
        hotelName: hotelName,
        pricingData: hotelData?.pricingData,
        destinationId,
        destinationName,
        checkInDate,
        checkOutDate,
      });

      // If room bookings were saved successfully, refresh the page to show updated saved room bookings
      if (result.success) {
        // Use window.location.reload() for a full page refresh to show updated saved room bookings
        window.location.reload();
      }
    } catch (error) {
      console.error("Failed to save room bookings:", error);
    }
  };

  // Handler for saving traveller info
  const handleSaveTravellerInfo = async () => {
    if (!cartDetails?.id) {
      toast.error("No cart available to save traveller information");
      return;
    }

    const travellerInfo = form.watch("travellerInfo");

    if (
      !travellerInfo?.primary_contact?.name ||
      !travellerInfo?.primary_contact?.email
    ) {
      toast.error("Primary contact name and email are required");
      return;
    }

    try {
      const result = await saveTravellerInfo({
        cartId: cartDetails.id,
        travellerInfo,
      });

      if (result.success) {
        // Optionally refresh the page or update the UI
        console.log("✅ Traveller info saved successfully");
      }
    } catch (error) {
      console.error("Failed to save traveller info:", error);
    }
  };

  // Helper function to generate pre-filled data from opportunity
  const generatePreFilledDataFromOpportunity = (opportunity: any) => {
    if (!opportunity) return {};

    const account = opportunity.account;
    const contact = opportunity.contact;
    const convertedFromLead = opportunity.convertedFromLead;

    // Helper function to parse address
    const parseAddress = (fullAddress: string) => {
      if (!fullAddress) return {};

      const lines = fullAddress
        .split("\n")
        .map((line) => line.trim())
        .filter(Boolean);
      if (lines.length === 0) return {};

      // Simple address parsing - can be enhanced
      const lastLine = lines[lines.length - 1];
      const cityStateZipPattern =
        /^(.+),\s*([A-Z]{2})\s+(\d{5}(?:-\d{4})?)(?:,\s*(.+))?$/i;
      const cityStateMatch = lastLine.match(cityStateZipPattern);

      if (cityStateMatch) {
        return {
          address: lines.slice(0, -1).join("\n") || lines[0] || "",
          city: cityStateMatch[1].trim(),
          state: cityStateMatch[2].trim(),
          postalCode: cityStateMatch[3].trim(),
          country: cityStateMatch[4]?.trim() || "",
        };
      }

      return {
        address: fullAddress,
        city: "",
        state: "",
        postalCode: "",
        country: "",
      };
    };

    const addressToParse =
      account?.billingAddress || account?.shippingAddress || "";
    const parsedAddress = parseAddress(addressToParse);

    // Determine the best business name to use
    let businessName = "";
    if (account?.name) {
      businessName = account.name;
    } else if (convertedFromLead?.company) {
      businessName = convertedFromLead.company;
    } else if (contact?.firstName || contact?.lastName) {
      businessName =
        `${contact.firstName || ""} ${contact.lastName || ""}`.trim();
    } else if (convertedFromLead?.firstName || convertedFromLead?.lastName) {
      businessName =
        `${convertedFromLead.firstName || ""} ${convertedFromLead.lastName || ""}`.trim();
    }

    return {
      name:
        opportunity.dealName || opportunity.name
          ? `Quote for ${opportunity.dealName || opportunity.name}`
          : "",
      description:
        opportunity.description ||
        (convertedFromLead
          ? `Converted from lead: ${convertedFromLead.firstName} ${convertedFromLead.lastName}`
          : ""),
      contactId: opportunity.contactId || "",
      accountId: opportunity.accountId || "",

      // Quotation To fields - prioritize lead/contact information over account
      quotationToBusinessName: businessName,
      quotationToAddress: parsedAddress.address || addressToParse || "",
      quotationToCity: parsedAddress.city || "",
      quotationToState: parsedAddress.state || account?.region || "",
      quotationToCountry: parsedAddress.country || account?.region || "",
      quotationToPostalCode: parsedAddress.postalCode || "",
      quotationToGSTIN: "", // Not available in current account structure
    };
  };

  // Generate traveller info from CRM data
  const generateTravellerInfoFromCRM = (opportunityData: any) => {
    if (!opportunityData) {
      return {
        primary_contact: { name: "", email: "", dob: "", country: "" },
        travellers: [],
      };
    }

    const contact = opportunityData.contact;
    const account = opportunityData.account;
    const convertedFromLead = opportunityData.convertedFromLead;

    // Priority order for primary contact data:
    // 1. Opportunity contact (if available)
    // 2. Converted lead data (if available)
    // 3. Account contact (if available)
    // 4. Fallback to empty values

    let primaryContactName = "";
    let primaryContactEmail = "";
    let primaryContactCountry = "";

    // Try to get name from contact
    if (contact?.firstName || contact?.lastName) {
      primaryContactName =
        `${contact.firstName || ""} ${contact.lastName || ""}`.trim();
    } else if (convertedFromLead?.firstName || convertedFromLead?.lastName) {
      primaryContactName =
        `${convertedFromLead.firstName || ""} ${convertedFromLead.lastName || ""}`.trim();
    } else if (account?.name) {
      primaryContactName = account.name;
    }

    // Try to get email from contact
    if (contact?.email) {
      primaryContactEmail = contact.email;
    } else if (convertedFromLead?.email) {
      primaryContactEmail = convertedFromLead.email;
    }

    // Try to get country from contact or account
    if (contact?.country) {
      primaryContactCountry = contact.country;
    } else if (account?.country) {
      primaryContactCountry = account.country;
    } else if (account?.region) {
      primaryContactCountry = account.region;
    }

    // Parse address to extract country if not already set
    if (!primaryContactCountry) {
      const addressToParse =
        account?.billingAddress || account?.shippingAddress || "";
      if (addressToParse) {
        const lines = addressToParse
          .split("\n")
          .map((line: string) => line.trim())
          .filter(Boolean);
        if (lines.length > 0) {
          const lastLine = lines[lines.length - 1];
          const cityStateZipPattern =
            /^(.+),\s*([A-Z]{2})\s+(\d{5}(?:-\d{4})?)(?:,\s*(.+))?$/i;
          const cityStateMatch = lastLine.match(cityStateZipPattern);
          if (cityStateMatch && cityStateMatch[4]) {
            primaryContactCountry = cityStateMatch[4].trim();
          }
        }
      }
    }

    return {
      primary_contact: {
        name: primaryContactName,
        email: primaryContactEmail,
        dob: "", // DOB is not typically stored in CRM
        country: primaryContactCountry,
      },
      travellers: [], // Additional travellers would need to be added manually
    };
  };

  // Storage key for form persistence
  const storageKey = `quote-form-${id}`;

  // Set up form persistence only for new records
  const shouldPersist = id === "new";

  // Flag to prevent saving after successful submission
  const [isSubmissionSuccessful, setIsSubmissionSuccessful] = useState(false);

  // Line items state - transform database line items to match form structure (for individual products only)
  const [lineItems, setLineItems] = useState<any[]>(() => {
    if (initialData?.lines && initialData.lines.length > 0) {
      // Filter only PRODUCT items when loading existing quote (edit mode)
      const productLines = initialData.lines.filter(
        (line: any) => line.itemType === "PRODUCT" || !line.itemType, // Backward compatibility for old quotes without itemType
      );
      return productLines.map((line: any) => ({
        id: line.id || crypto.randomUUID(),
        description: line.description || "",
        quantity: line.quantity || 1,
        unitPrice: line.unitPrice || 0,
        lineTotal: line.lineTotal || 0,
        productId: line.productId || "",
        unitType: line.unitType || "",
        discountType: line.discountType || "PERCENTAGE",
        discountValue: line.discountValue || 0,
        taxRate: line.taxRate || 0,
        subtotal: line.subtotal || 0,
        isManualPricing: line.isManualPricing || false,
        manualLineTotal: line.manualLineTotal || null,
      }));
    } else if (opportunityProducts && opportunityProducts.length > 0) {
      // Pre-populate with individual opportunity products only (packages handled separately)
      return opportunityProducts.map((product: any) => ({
        id: product.id, // Use the generated ID from server
        description: product.description,
        quantity: product.quantity,
        unitPrice: product.unitPrice,
        lineTotal: product.lineTotal,
        productId: product.productId,
        unitType: product.unitType || "",
        discountType: "PERCENTAGE",
        discountValue: 0,
        taxRate: 0,
        subtotal: product.quantity * product.unitPrice,
      }));
    }
    // Default empty line item for new quotes without opportunity
    return [
      {
        id: crypto.randomUUID(),
        description: "",
        quantity: 1,
        unitPrice: 0,
        lineTotal: 0,
        productId: "",
        unitType: "",
        discountType: "PERCENTAGE",
        discountValue: 0,
        taxRate: 0,
        subtotal: 0,
      },
    ];
  });

  // Package items state (for packages from opportunities or existing quotes)
  const [packageItems, setPackageItems] = useState<any[]>(() => {
    if (initialData?.lines && initialData.lines.length > 0) {
      // Filter only PACKAGE items when loading existing quote (edit mode)
      const packageLines = initialData.lines.filter(
        (line: any) => line.itemType === "PACKAGE",
      );
      return packageLines.map((line: any) => ({
        id: line.id || crypto.randomUUID(),
        packageId: line.packageId || null,
        packageName: line.packageName || line.description || "Package",
        description: line.description || "",
        quantity: line.quantity || 1,
        unitPrice: line.unitPrice || 0,
        lineTotal: line.lineTotal || 0,
        discountType: line.discountType || "PERCENTAGE",
        discountValue: line.discountValue || 0,
        taxRate: line.taxRate || 0,
        subtotal: line.subtotal || 0,
      }));
    } else if (opportunityPackages && opportunityPackages.length > 0) {
      // Transform opportunity packages to package line items (as single items, not expanded)
      return opportunityPackages.map((packageItem: any) => ({
        id: crypto.randomUUID(), // Generate new ID for quote line item
        packageId: packageItem.packageId || packageItem.id,
        packageName: packageItem.packageName || packageItem.name || "Package",
        description: packageItem.description || "",
        quantity: packageItem.quantity || 1,
        unitPrice: packageItem.unitPrice || packageItem.totalPrice || 0,
        lineTotal: packageItem.lineTotal || packageItem.totalPrice || 0,
        discountType: packageItem.discountType || "PERCENTAGE",
        discountValue: packageItem.discountValue || 0,
        taxRate: packageItem.taxRate || 0,
        subtotal:
          packageItem.subtotal ||
          packageItem.quantity *
            (packageItem.unitPrice || packageItem.totalPrice),
        lineTotalBeforeDiscount: packageItem.lineTotalBeforeDiscount,
        discount: packageItem.discount,
      }));
    }
    return [];
  });

  // Use the useSaveFormData hook with built-in toast notifications
  const { save: saveQuote, isLoading: isSavingFromHook } = useSaveFormData({
    model: "quote",
    clearFormPersistence: shouldPersist
      ? () => {
          if (typeof window !== "undefined") {
            setIsSubmissionSuccessful(true);
            sessionStorage.removeItem(storageKey);
            sessionStorage.removeItem("quote-form-new");
          }
        }
      : undefined,
    onSuccess: () => {
      // Navigate back to quotes list
      router.push("/quotes");
    },
  });

  // Generate pre-filled data from opportunity if available
  const opportunityPreFill =
    generatePreFilledDataFromOpportunity(opportunityData);

  // Initialize React Hook Form with Zod validation
  const form = useForm<QuoteFormValues>({
    // resolver: zodResolver(quoteFormSchema),
    defaultValues: {
      description:
        initialData?.description || opportunityPreFill.description || "",
      status: initialData?.status || "DRAFT",
      contactId: initialData?.contactId || opportunityPreFill.contactId || "",
      accountId: initialData?.accountId || opportunityPreFill.accountId || "",
      opportunityId: opportunityId || initialData?.opportunityId || "",
      selectedDestination: initialData?.selectedDestination || "",
      selectedHotel: initialData?.selectedHotel || "",
      checkInDate: initialData?.checkInDate || "",
      checkOutDate: initialData?.checkOutDate || "",
      adults: initialData?.adults || 1,
      children: initialData?.children || 0,
      travellerInfo:
        initialData?.travellerInfo ||
        generateTravellerInfoFromCRM(opportunityData),
      addonCategory: "",
      validUntil: initialData?.validUntil
        ? new Date(initialData.validUntil).toISOString().split("T")[0]
        : "",
      paymentTerms: initialData?.paymentTerms || "Net 30",
      deliveryTerms: initialData?.deliveryTerms || "",

      // Quotation From fields - auto-populated from organization details
      quotationFromCountry:
        initialData?.quotationFromCountry ||
        organizationDetails?.country ||
        "US", // Fallback to US if no country provided
      quotationFromBusinessName:
        initialData?.quotationFromBusinessName ||
        organizationDetails?.businessName ||
        organizationDetails?.name ||
        "",
      quotationFromGSTIN:
        initialData?.quotationFromGSTIN || organizationDetails?.gstin || "",
      quotationFromAddress:
        initialData?.quotationFromAddress || organizationDetails?.address || "",
      quotationFromCity:
        initialData?.quotationFromCity || organizationDetails?.city || "",
      quotationFromPostalCode:
        initialData?.quotationFromPostalCode ||
        organizationDetails?.postalCode ||
        "",
      quotationFromState:
        initialData?.quotationFromState || organizationDetails?.state || "",

      // Quotation To fields
      quotationToCountry:
        initialData?.quotationToCountry ||
        opportunityPreFill.quotationToCountry ||
        "",
      quotationToBusinessName:
        initialData?.quotationToBusinessName ||
        opportunityPreFill.quotationToBusinessName ||
        "",
      quotationToGSTIN:
        initialData?.quotationToGSTIN ||
        opportunityPreFill.quotationToGSTIN ||
        "",
      quotationToAddress:
        initialData?.quotationToAddress ||
        opportunityPreFill.quotationToAddress ||
        "",
      quotationToCity:
        initialData?.quotationToCity ||
        opportunityPreFill.quotationToCity ||
        "",
      quotationToPostalCode:
        initialData?.quotationToPostalCode ||
        opportunityPreFill.quotationToPostalCode ||
        "",
      quotationToState:
        initialData?.quotationToState ||
        opportunityPreFill.quotationToState ||
        "",

      subtotal: initialData?.subtotal || 0,
      totalTax: initialData?.totalTax || 0,
      totalDiscount: initialData?.totalDiscount || 0,
      grandTotal: initialData?.grandTotal || 0,

      // Enhanced pricing fields
      quoteDiscountType: initialData?.quoteDiscountType || "PERCENTAGE",
      quoteDiscountValue: initialData?.quoteDiscountValue || 0,
      intraStateTaxRate: initialData?.intraStateTaxRate || 0,
      interStateTaxRate: initialData?.interStateTaxRate || 0,
      adjustments: initialData?.adjustments || 0,
      isManualTotal: initialData?.isManualTotal || false,
      manualTotalValue: initialData?.manualTotalValue,
      isManualGrandTotal: initialData?.isManualGrandTotal || false,
      manualGrandTotal: initialData?.manualGrandTotal,

      taxPercentage: 0, // This is a form-only field for calculation
      lineItems: lineItems,
    },
  });

  // Simple form persistence for new quotes
  useEffect(() => {
    if (
      !shouldPersist ||
      typeof window === "undefined" ||
      isSubmissionSuccessful
    )
      return;

    // Watch for form changes and save to storage
    const subscription = form.watch((data) => {
      try {
        sessionStorage.setItem(storageKey, JSON.stringify(data));
      } catch (error) {
        console.error("Error saving form data:", error);
      }
    });

    return () => subscription.unsubscribe();
  }, [shouldPersist, form, storageKey, isSubmissionSuccessful]);

  // Validate guest capacity when guest details change
  useEffect(() => {
    const subscription = form.watch((data) => {
      const adults = data.adults || 0;
      const children = data.children || 0;

      if (adults < 1) {
        setCapacityValidation({ errors: [], warnings: [] });
        return;
      }

      const guestDetails = { adults, children };
      const totalGuests = guestDetails.adults + guestDetails.children;

      const errors: string[] = [];
      const warnings: string[] = [];

      if (totalGuests > 10) {
        errors.push("Maximum 10 guests allowed per room");
      }

      if (guestDetails.adults > 6) {
        errors.push("Maximum 6 adults allowed per room");
      }

      if (guestDetails.children > 4) {
        warnings.push("Large number of children may limit room availability");
      }

      setCapacityValidation({ errors, warnings });
    });

    return () => subscription.unsubscribe();
  }, [form]);

  // Restore saved form data on mount (for form persistence only)
  useEffect(() => {
    if (!shouldPersist || typeof window === "undefined") return;

    try {
      const savedData = sessionStorage.getItem(storageKey);
      if (savedData && savedData !== "{}") {
        const parsedData = JSON.parse(savedData);
        Object.keys(parsedData).forEach((key) => {
          if (
            parsedData[key] !== undefined &&
            parsedData[key] !== null &&
            parsedData[key] !== ""
          ) {
            form.setValue(key as any, parsedData[key]);
          }
        });
      }
    } catch (error) {
      console.error("Error restoring form data:", error);
    }
  }, [shouldPersist, form, storageKey]);

  // Effect to update tax rates when country configuration changes
  useEffect(() => {
    if (countryConfig?.configuration && !isEditMode) {
      // Only auto-populate for new quotes, not when editing existing ones
      const config = countryConfig.configuration;

      // Set default tax rates based on country configuration
      if (config.primaryTaxRate > 0) {
        form.setValue("intraStateTaxRate", config.primaryTaxRate);

        // For countries with secondary tax, set inter-state rate
        if (config.secondaryTaxRate && config.secondaryTaxRate > 0) {
          form.setValue("interStateTaxRate", config.secondaryTaxRate);
        } else {
          // For single tax systems, use the same rate for both
          form.setValue("interStateTaxRate", config.primaryTaxRate);
        }
      }
    }
  }, [countryConfig, isEditMode, form]);

  // Effect to reset form when initialData changes (only for edit mode)
  useEffect(() => {
    if (initialData && isEditMode) {
      form.reset({
        description: initialData.description || "",
        status: initialData.status || "DRAFT",
        contactId: initialData.contactId || "",
        accountId: initialData.accountId || "",
        opportunityId: initialData.opportunityId || "",
        selectedHotel: initialData.selectedHotel || "",
        addonCategory: "",
        validUntil: initialData.validUntil
          ? new Date(initialData.validUntil).toISOString().split("T")[0]
          : "",
        paymentTerms: initialData.paymentTerms || "Net 30",
        deliveryTerms: initialData.deliveryTerms || "",
        quotationFromCountry:
          initialData.quotationFromCountry ||
          organizationDetails?.country ||
          "US", // Fallback to US if no country provided
        quotationFromBusinessName:
          initialData.quotationFromBusinessName ||
          organizationDetails?.businessName ||
          organizationDetails?.name ||
          "Your Business", // Fallback business name
        quotationFromGSTIN:
          initialData.quotationFromGSTIN || organizationDetails?.gstin || "",
        quotationFromAddress:
          initialData.quotationFromAddress ||
          organizationDetails?.address ||
          "",
        quotationFromCity:
          initialData.quotationFromCity || organizationDetails?.city || "",
        quotationFromPostalCode:
          initialData.quotationFromPostalCode ||
          organizationDetails?.postalCode ||
          "",
        quotationFromState:
          initialData.quotationFromState || organizationDetails?.state || "",
        quotationToCountry: initialData.quotationToCountry || "",
        quotationToBusinessName: initialData.quotationToBusinessName || "",
        quotationToGSTIN: initialData.quotationToGSTIN || "",
        quotationToAddress: initialData.quotationToAddress || "",
        quotationToCity: initialData.quotationToCity || "",
        quotationToPostalCode: initialData.quotationToPostalCode || "",
        quotationToState: initialData.quotationToState || "",
        subtotal: initialData.subtotal || 0,
        totalTax: initialData.totalTax || 0,
        totalDiscount: initialData.totalDiscount || 0,
        grandTotal: initialData.grandTotal || 0,
        taxPercentage: 0, // Default tax percentage
        lineItems: lineItems, // Will be handled separately by lineItems state
      });

      // Also update line items and package items state
      if (initialData.lines && initialData.lines.length > 0) {
        // Filter and transform product lines only
        const productLines = initialData.lines.filter(
          (line: any) => line.itemType === "PRODUCT" || !line.itemType, // Backward compatibility
        );
        const transformedLines = productLines.map((line: any) => ({
          id: line.id || crypto.randomUUID(),
          description: line.description || "",
          quantity: line.quantity || 1,
          unitPrice: line.unitPrice || 0,
          lineTotal: line.lineTotal || 0,
          productId: line.productId || "",
          unitType: line.unitType || "",
          discountType: line.discountType || "PERCENTAGE",
          discountValue: line.discountValue || 0,
          taxRate: line.taxRate || 0,
          subtotal: line.subtotal || 0,
          isManualPricing: line.isManualPricing || false,
          manualLineTotal: line.manualLineTotal || null,
        }));
        setLineItems(transformedLines);

        // Filter and transform package lines only
        const packageLines = initialData.lines.filter(
          (line: any) => line.itemType === "PACKAGE",
        );
        const transformedPackages = packageLines.map((line: any) => ({
          id: line.id || crypto.randomUUID(),
          packageId: line.packageId || null,
          packageName: line.packageName || line.description || "Package",
          description: line.description || "",
          quantity: line.quantity || 1,
          unitPrice: line.unitPrice || 0,
          lineTotal: line.lineTotal || 0,
          discountType: line.discountType || "PERCENTAGE",
          discountValue: line.discountValue || 0,
          taxRate: line.taxRate || 0,
          subtotal: line.subtotal || 0,
        }));
        setPackageItems(transformedPackages);
      }
    }
  }, [initialData, form, organizationDetails, isEditMode]);

  // Calculate totals when line items or package items change
  const calculateTotals = React.useCallback(
    (updatedLineItems?: any[], updatedPackageItems?: any[]) => {
      const itemsToCalculate = updatedLineItems || lineItems;
      const packagesToCalculate = updatedPackageItems || packageItems;

      // Calculate subtotal from both line items and package items
      const lineItemsSubtotal = itemsToCalculate.reduce(
        (sum, item) => sum + (item.lineTotal || 0),
        0,
      );

      const packageItemsSubtotal = packagesToCalculate.reduce(
        (sum, item) => sum + (item.lineTotal || 0),
        0,
      );

      // Calculate subtotal from cart items (inventory API items)
      // Only include non-addon cart items to avoid double counting
      // Addons are handled separately in the local addons state
      const cartItemsSubtotal =
        cartDetails?.items?.reduce((sum, item) => {
          // Skip addon items to avoid double counting
          // Check for legacy hardcoded product_id or addon type in metadata
          if (
            item.product_id === "product_add_ons_main" ||
            item.metadata?.type === "addon"
          ) {
            return sum;
          }
          // Cart items are in minor units (pence/cents), convert to major units (dollars/pounds)
          const itemTotal = (item.unit_price * item.quantity || 0) / 100;
          return sum + itemTotal;
        }, 0) || 0;

      // Calculate subtotal from local addons state
      const addonsSubtotal = addons.reduce(
        (sum, addon) => sum + (addon.total || 0),
        0,
      );

      const subtotal =
        lineItemsSubtotal +
        packageItemsSubtotal +
        cartItemsSubtotal +
        addonsSubtotal;

      // Get quote-level discount with safe number conversion
      const quoteDiscountType =
        form.getValues("quoteDiscountType") || "PERCENTAGE";
      const quoteDiscountValue = safeParseNumber(
        form.getValues("quoteDiscountValue"),
        0,
      );

      let quoteDiscount = 0;
      if (quoteDiscountValue > 0) {
        if (quoteDiscountType === "PERCENTAGE") {
          quoteDiscount = (subtotal * quoteDiscountValue) / 100;
        } else {
          quoteDiscount = quoteDiscountValue;
        }
      }

      // Calculate tax rates with safe number conversion
      const intraStateTaxRate = safeParseNumber(
        form.getValues("intraStateTaxRate"),
        0,
      );
      const interStateTaxRate = safeParseNumber(
        form.getValues("interStateTaxRate"),
        0,
      );
      const adjustments = safeParseNumber(form.getValues("adjustments"), 0);

      const amountAfterDiscount = subtotal - quoteDiscount;
      const intraStateTax = (amountAfterDiscount * intraStateTaxRate) / 100;
      const interStateTax = (amountAfterDiscount * interStateTaxRate) / 100;
      const totalTax = intraStateTax + interStateTax;

      // Check for manual total override
      const isManualTotal = form.getValues("isManualTotal") || false;
      const manualTotalValue = safeParseNumber(
        form.getValues("manualTotalValue"),
      );

      // Check for manual grand total override
      const isManualGrandTotal = form.getValues("isManualGrandTotal") || false;
      const manualGrandTotal = safeParseNumber(
        form.getValues("manualGrandTotal"),
      );

      const calculatedGrandTotal = amountAfterDiscount + totalTax + adjustments;
      const grandTotal =
        isManualGrandTotal && manualGrandTotal > 0
          ? manualGrandTotal
          : isManualTotal && manualTotalValue > 0
            ? manualTotalValue
            : calculatedGrandTotal;

      // Update form values with proper number formatting
      form.setValue("subtotal", Number(subtotal.toFixed(2)));
      form.setValue("totalDiscount", Number(quoteDiscount.toFixed(2)));
      form.setValue("totalTax", Number(totalTax.toFixed(2)));
      form.setValue("grandTotal", Number(grandTotal.toFixed(2)));
      form.setValue("lineItems", itemsToCalculate);
    },
    [lineItems, packageItems, cartDetails, addons, form],
  );

  // Watch for pricing field changes
  const quoteDiscountType = form.watch("quoteDiscountType");
  const quoteDiscountValue = form.watch("quoteDiscountValue");
  const intraStateTaxRate = form.watch("intraStateTaxRate");
  const interStateTaxRate = form.watch("interStateTaxRate");
  const adjustments = form.watch("adjustments");
  const isManualTotal = form.watch("isManualTotal");
  const manualTotalValue = form.watch("manualTotalValue");
  const isManualGrandTotal = form.watch("isManualGrandTotal");
  const manualGrandTotal = form.watch("manualGrandTotal");

  // Update totals when any pricing field changes
  React.useEffect(() => {
    calculateTotals();
  }, [
    calculateTotals,
    quoteDiscountType,
    quoteDiscountValue,
    intraStateTaxRate,
    interStateTaxRate,
    adjustments,
    isManualTotal,
    manualTotalValue,
    isManualGrandTotal,
    manualGrandTotal,
  ]);

  const onSubmit = async (data: QuoteFormValues) => {
    try {
      // Validate and clean numeric fields before submission
      const cleanedData = {
        ...data,
        quoteDiscountValue: safeParseNumber(data.quoteDiscountValue, 0),
        intraStateTaxRate: safeParseNumber(data.intraStateTaxRate, 0),
        interStateTaxRate: safeParseNumber(data.interStateTaxRate, 0),
        adjustments: safeParseNumber(data.adjustments, 0),
        manualTotalValue: data.manualTotalValue
          ? safeParseNumber(data.manualTotalValue, 0)
          : undefined,
        isManualGrandTotal: data.isManualGrandTotal || false,
        manualGrandTotal: data.manualGrandTotal
          ? safeParseNumber(data.manualGrandTotal, 0)
          : undefined,
      };

      // Prepare the quote data with line items
      const quoteData = {
        ...cleanedData,
        // Include opportunity ID if provided
        opportunityId: opportunityId || data.opportunityId || null,
        // Convert date string to Date object
        validUntil: data.validUntil ? new Date(data.validUntil) : null,
        // Include selected destination and hotel data if available
        selectedDestination: data.selectedDestination || null,
        selectedDestinationData: selectedDestinationData || null,
        selectedHotelData: selectedHotelData || null,
        // Include addons data if available
        addons: addons || [],
        // Prepare all line items for creation (products, packages, and hotel rooms)
        lines: [
          // Individual product line items
          ...lineItems.map((item, index) => ({
            lineNumber: index + 1,
            description: item.description,
            quantity: safeParseNumber(item.quantity, 1),
            unitPrice: safeParseNumber(item.unitPrice, 0),
            lineTotal: safeParseNumber(item.lineTotal, 0),
            productId: item.productId || null,
            priceBookId: item.priceBookId || null,
            unitType: item.unitType || null,
            discountType: item.discountType || "PERCENTAGE",
            discountValue: safeParseNumber(item.discountValue, 0),
            taxRate: safeParseNumber(item.taxRate, 0),
            subtotal: safeParseNumber(item.subtotal, 0),
            itemType: "PRODUCT", // Mark as product line item
            isManualPricing: item.isManualPricing || false,
            manualLineTotal: item.manualLineTotal
              ? safeParseNumber(item.manualLineTotal, 0)
              : null,
          })),
          // Package line items
          ...packageItems.map((item, index) => ({
            lineNumber: lineItems.length + index + 1, // Continue numbering after products
            description: item.packageName || item.description,
            quantity: safeParseNumber(item.quantity, 1),
            unitPrice: safeParseNumber(item.unitPrice, 0),
            lineTotal: safeParseNumber(item.lineTotal, 0),
            productId: null, // Packages don't have productId
            unitType: null,
            discountType: item.discountType || "PERCENTAGE",
            discountValue: safeParseNumber(item.discountValue, 0),
            taxRate: safeParseNumber(item.taxRate, 0),
            subtotal: safeParseNumber(item.subtotal, 0),
            itemType: "PACKAGE", // Mark as package line item
            packageId: item.packageId || null,
            packageName: item.packageName || null,
            packageContents: null, // Don't store package contents
          })),
          // Hotel room line items
          ...hotelRoomBlocks.map((block, index) => ({
            lineNumber: lineItems.length + packageItems.length + index + 1, // Continue numbering
            description: `${block.room_name} (${block.config_name}) - ${block.check_in} to ${block.check_out}`,
            quantity: block.nights,
            unitPrice: safeParseNumber(block.rate, 0),
            lineTotal: safeParseNumber(block.total, 0),
            productId: null, // Hotel rooms don't have productId
            unitType: "nights",
            discountType: "PERCENTAGE",
            discountValue: 0,
            taxRate: 0,
            subtotal: safeParseNumber(block.total, 0),
            itemType: "HOTEL_ROOM", // Mark as hotel room line item
            // Hotel-specific metadata
            hotelRoomId: block.room_id,
            hotelRoomName: block.room_name,
            hotelConfigId: block.product_id,
            hotelConfigName: block.config_name,
            hotelCheckIn: block.check_in,
            hotelCheckOut: block.check_out,
            hotelNights: block.nights,
          })),
          // Cart items from inventory API
          ...(cartDetails?.items?.map((item, index) => ({
            lineNumber:
              lineItems.length +
              packageItems.length +
              hotelRoomBlocks.length +
              index +
              1, // Continue numbering
            description: item.title || item.product_title || "Cart Item",
            quantity: safeParseNumber(item.quantity, 1),
            unitPrice: safeParseNumber(item.unit_price, 0),
            lineTotal: safeParseNumber(item.unit_price * item.quantity, 0),
            productId: item.product_id || null,
            unitType: null,
            discountType: "PERCENTAGE",
            discountValue: 0,
            taxRate: 0,
            subtotal: safeParseNumber(item.unit_price * item.quantity, 0),
            itemType: "CART_ITEM", // Mark as cart item from inventory API
            // Cart item metadata
            cartItemId: item.id,
            variantId: item.variant_id,
            productType: item.product_type,
            metadata: item.metadata || {},
          })) || []),
        ],
      };

      // Remove the form-only fields and ensure proper number formatting
      const { lineItems: _, taxPercentage: __, ...quoteDataRaw } = quoteData;

      // Ensure all numeric fields are properly formatted
      const finalQuoteData = {
        ...quoteDataRaw,
        quoteDiscountValue: safeParseNumber(quoteDataRaw.quoteDiscountValue, 0),
        intraStateTaxRate: safeParseNumber(quoteDataRaw.intraStateTaxRate, 0),
        interStateTaxRate: safeParseNumber(quoteDataRaw.interStateTaxRate, 0),
        adjustments: safeParseNumber(quoteDataRaw.adjustments, 0),
        manualTotalValue: quoteDataRaw.manualTotalValue
          ? safeParseNumber(quoteDataRaw.manualTotalValue, 0)
          : undefined,
        isManualGrandTotal: quoteDataRaw.isManualGrandTotal || false,
        manualGrandTotal: quoteDataRaw.manualGrandTotal
          ? safeParseNumber(quoteDataRaw.manualGrandTotal, 0)
          : undefined,
      };

      await saveQuote(id, finalQuoteData);
    } catch (error) {
      console.error("Error saving quote:", error);
    }
  };

  return (
    <div className="space-y-4">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          {/* Responsive 2-column layout */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
            {/* Left Column (8/12) - Core quote information */}
            <div className="lg:col-span-8 space-y-6">
              {/* Quote Information Section */}
              <SectionWrapper
                title="Quotation Information"
                description="Basic details of the quotation."
              >
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Quote Number Field - Read Only */}
                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                      Quotation Number <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="text"
                      value={
                        initialData?.quoteNumber || "Will be auto-generated"
                      }
                      disabled={true}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Auto-generated quote number"
                    />
                  </div>

                  {/* Status field - Only show for existing quotes */}
                  {isEditMode && initialData && (
                    <div className="flex flex-col space-y-2">
                      <label className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                        Status
                      </label>
                      <div className="w-full">
                        <QuotationStatusSwitch
                          currentStatus={
                            form.watch("status") as QuotationStatus
                          }
                          onStatusChange={handleStatusChange}
                          disabled={isSaving}
                          className="w-full"
                        />
                      </div>
                    </div>
                  )}
                </div>
                <div className="mt-6">
                  <TextAreaFieldFormElement
                    name="description"
                    label="Description"
                    control={form.control}
                    placeholder="Provide a brief description of the quotation..."
                  />
                </div>
              </SectionWrapper>

              {/* Traveller Information Section - Only show when inventory is configured */}
              {inventoryConfig?.isConfigured && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Traveller Information</CardTitle>
                        <CardDescription>
                          Collect primary contact and additional traveller
                          details for the booking
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="mt-2">
                    <TravellerInfoForm
                      control={form.control}
                      name="travellerInfo"
                    />
                    <div className="flex justify-end mt-6">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              type="button"
                              onClick={handleSaveTravellerInfo}
                              disabled={isSavingTravellerInfo}
                              className="flex items-center gap-2"
                            >
                              {isSavingTravellerInfo ? (
                                <>
                                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                                  Saving...
                                </>
                              ) : (
                                <>
                                  <div className="h-4 w-4" />
                                  Save Traveller Information
                                </>
                              )}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p className="max-w-xs">
                              Save traveller information to the cart. This data
                              will be used for booking confirmation and travel
                              arrangements.
                            </p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Hotel Booking Section - Only show when inventory is configured */}
              {inventoryConfig?.isConfigured && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Hotel Booking</CardTitle>
                        <CardDescription>
                          {hasSavedRoomBookings()
                            ? "Manage your hotel bookings and check availability"
                            : "Select hotel and room availability for this quotation"}
                        </CardDescription>
                      </div>
                      {/* Check Availability Button - Only show when saved bookings exist */}
                      {hasSavedRoomBookings() && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={handleCheckAvailability}
                          className="flex items-center gap-2"
                        >
                          <Calendar className="h-4 w-4" />
                          Check Availability
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="mt-2">
                    <div className="space-y-6">
                      {/* Step 1: Destination and Hotel Selection */}
                      {(!hasSavedRoomBookings() || showAvailabilityCheck) && (
                        <div className="space-y-4">
                          <div className="flex items-center gap-2 mb-4">
                            <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                              1
                            </div>
                            <h3 className="text-lg font-medium">
                              Select Destination & Hotel
                            </h3>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <DynamicSelectFieldFormElement
                              name="selectedDestination"
                              label="Destination"
                              control={form.control}
                              placeholder="Select a destination first"
                              dynamicKey="destinations"
                              required={true}
                              helperText="Select a destination to see available hotels"
                              onSelectionChange={(selectedOption) => {
                                // Store the full destination data for later use
                                setSelectedDestinationData(selectedOption);
                                // Reset all subsequent selections when destination changes
                                form.setValue("selectedHotel", "");
                                form.setValue("checkInDate", "");
                                form.setValue("checkOutDate", "");
                                form.setValue("adults", 1);
                                form.setValue("children", 0);
                                setSelectedHotelData(null);
                                console.log(
                                  "Selected destination:",
                                  selectedOption,
                                );
                              }}
                            />
                            {form.watch("selectedDestination") ? (
                              <DynamicSelectFieldFormElement
                                name="selectedHotel"
                                label="Hotel"
                                control={form.control}
                                placeholder="Search and select a hotel"
                                dynamicKey="hotels"
                                payload={{
                                  destination_id: form.watch(
                                    "selectedDestination",
                                  ),
                                }}
                                onSelectionChange={(selectedOption) => {
                                  // Store the full hotel data for later use
                                  setSelectedHotelData(selectedOption);
                                  // Reset date and guest selections when hotel changes
                                  form.setValue("checkInDate", "");
                                  form.setValue("checkOutDate", "");
                                  form.setValue("adults", 1);
                                  form.setValue("children", 0);
                                  console.log(
                                    "Selected hotel:",
                                    selectedOption,
                                  );
                                }}
                                helperText="Hotels are filtered based on the selected destination"
                              />
                            ) : (
                              <div className="space-y-2">
                                <label className="text-sm font-medium text-muted-foreground">
                                  Hotel
                                  <span className="text-destructive">*</span>
                                </label>
                                <div className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm text-muted-foreground">
                                  Please select a destination first
                                </div>
                                <p className="text-xs text-muted-foreground">
                                  Choose a destination to see available hotels
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Step 2: Date Range Selection */}
                      {(!hasSavedRoomBookings() || showAvailabilityCheck) &&
                        form.watch("selectedDestination") &&
                        form.watch("selectedHotel") && (
                          <div className="space-y-4">
                            <div className="flex items-center gap-2 mb-4">
                              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                2
                              </div>
                              <h3 className="text-lg font-medium">
                                Travel Dates
                              </h3>
                            </div>
                            <HotelDateRangePicker control={form.control} />
                          </div>
                        )}

                      {/* Step 3: Guest Details */}
                      {(!hasSavedRoomBookings() || showAvailabilityCheck) &&
                        form.watch("selectedDestination") &&
                        form.watch("selectedHotel") &&
                        form.watch("checkInDate") &&
                        form.watch("checkOutDate") && (
                          <div className="space-y-4">
                            <div className="flex items-center gap-2 mb-4">
                              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                3
                              </div>
                              <h3 className="text-lg font-medium">
                                Occupancy Details
                              </h3>
                            </div>
                            <GuestDetailsForm
                              control={form.control}
                              capacityErrors={capacityValidation.errors}
                              capacityWarnings={capacityValidation.warnings}
                            />
                          </div>
                        )}

                      {/* Step 4: Room Availability Grid */}
                      {(!hasSavedRoomBookings() || showAvailabilityCheck) &&
                        form.watch("selectedDestination") &&
                        form.watch("selectedHotel") &&
                        form.watch("checkInDate") &&
                        form.watch("checkOutDate") &&
                        form.watch("adults") >= 1 && (
                          <div className="space-y-4">
                            <div className="flex items-center gap-2 mb-4">
                              <div className="w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                                4
                              </div>
                              <h3 className="text-lg font-medium">
                                Room Availability
                              </h3>
                            </div>
                            <HotelBookingCalendar
                              selectedHotel={form.watch("selectedHotel")}
                              guestDetails={{
                                adults: form.watch("adults") || 1,
                                children: form.watch("children") || 0,
                              }}
                              initialRoomBlocks={hotelRoomBlocks}
                              onRoomBlocksChange={setHotelRoomBlocks}
                              onAddToQuotation={
                                handleAddRoomBookingsToQuotation
                              }
                              isLoadingSave={isSavingRoomBookings}
                              className="mt-6"
                            />
                          </div>
                        )}

                      {/* Guidance message for incomplete steps */}
                      {(!hasSavedRoomBookings() || showAvailabilityCheck) &&
                        (() => {
                          const destination = form.watch("selectedDestination");
                          const hotel = form.watch("selectedHotel");
                          const checkIn = form.watch("checkInDate");
                          const checkOut = form.watch("checkOutDate");
                          const adults = form.watch("adults");

                          if (!destination) {
                            return (
                              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <div className="flex items-center space-x-2">
                                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                  <p className="text-sm text-blue-700 font-medium">
                                    Step 1: Please select a destination to
                                    continue with hotel booking
                                  </p>
                                </div>
                                <p className="text-xs text-blue-600 mt-1">
                                  Hotels will be filtered based on your
                                  destination selection
                                </p>
                              </div>
                            );
                          }

                          if (!hotel) {
                            return (
                              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <div className="flex items-center space-x-2">
                                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                  <p className="text-sm text-blue-700 font-medium">
                                    Step 2: Please select a hotel to continue
                                  </p>
                                </div>
                              </div>
                            );
                          }

                          if (!checkIn || !checkOut) {
                            return (
                              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <div className="flex items-center space-x-2">
                                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                  <p className="text-sm text-blue-700 font-medium">
                                    Step 3: Please select check-in and check-out
                                    dates
                                  </p>
                                </div>
                              </div>
                            );
                          }

                          if (!adults || adults < 1) {
                            return (
                              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                                <div className="flex items-center space-x-2">
                                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                  <p className="text-sm text-blue-700 font-medium">
                                    Step 4: Please specify guest details (at
                                    least 1 adult required)
                                  </p>
                                </div>
                              </div>
                            );
                          }

                          return null;
                        })()}

                      {/* Saved Room Bookings - Show cart items */}
                      <SavedRoomBookings
                        cartDetails={cartDetails}
                        formatCurrency={activeFormatCurrency}
                        currency={
                          cartDetails?.currency_code ||
                          countryConfig?.configuration?.currencyCode ||
                          "USD"
                        }
                        className="mt-4"
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Add-ons Section - Only show when inventory is configured */}
              {inventoryConfig?.isConfigured && (
                <Card>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>Add-ons</CardTitle>
                        <CardDescription>
                          Add booking add-ons for this quotation
                        </CardDescription>
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => setShowAddonForm(true)}
                        className="flex items-center gap-2"
                      >
                        <PlusIcon className="h-4 w-4" />
                        Add Add-on
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Add-on Form */}
                    {showAddonForm && (
                      <div className="border rounded-lg p-4 bg-gray-50">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          {/* Category Selection */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Category <span className="text-red-500">*</span>
                            </label>
                            <DynamicSelectFieldFormElement
                              name="addonCategory"
                              label=""
                              control={form.control}
                              placeholder="Select category"
                              dynamicKey="addon-categories"
                              onSelectionChange={(selectedOption) => {
                                handleCategoryChange(
                                  selectedOption?.value || "",
                                );
                              }}
                            />
                          </div>

                          {/* Product & Service Selection */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Product & Service{" "}
                              <span className="text-red-500">*</span>
                            </label>
                            <select
                              value={addonForm.productService}
                              onChange={(e) =>
                                setAddonForm((prev) => ({
                                  ...prev,
                                  productService: e.target.value,
                                }))
                              }
                              disabled={
                                !addonForm.category || isLoadingProducts
                              }
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                            >
                              <option value="">
                                {isLoadingProducts
                                  ? "Loading..."
                                  : "Select product/service"}
                              </option>
                              {addonProducts.map((product) => (
                                <option
                                  key={product.value}
                                  value={product.value}
                                >
                                  {product.label}
                                </option>
                              ))}
                            </select>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          {/* Start Date */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Start Date <span className="text-red-500">*</span>
                            </label>
                            <input
                              type="date"
                              value={addonForm.startDate}
                              onChange={(e) =>
                                setAddonForm((prev) => ({
                                  ...prev,
                                  startDate: e.target.value,
                                }))
                              }
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </div>

                          {/* End Date */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              End Date <span className="text-red-500">*</span>
                            </label>
                            <input
                              type="date"
                              value={addonForm.endDate}
                              onChange={(e) =>
                                setAddonForm((prev) => ({
                                  ...prev,
                                  endDate: e.target.value,
                                }))
                              }
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            />
                          </div>

                          {/* Find Offerings Button */}
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Find Offerings
                            </label>
                            <Button
                              type="button"
                              variant="outline"
                              onClick={handleFindOfferings}
                              disabled={
                                !addonForm.productService ||
                                !addonForm.startDate ||
                                !addonForm.endDate ||
                                isLoadingOfferings
                              }
                              className="w-full"
                            >
                              {isLoadingOfferings ? (
                                <>
                                  <Loader2Icon className="w-4 h-4 mr-2 animate-spin" />
                                  Finding...
                                </>
                              ) : (
                                "🔍 Find"
                              )}
                            </Button>
                          </div>
                        </div>

                        {/* Cost Price */}
                        <div className="mb-4">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Cost Price <span className="text-red-500">*</span>
                          </label>
                          <input
                            type="number"
                            step="0.01"
                            value={addonForm.price}
                            onChange={(e) =>
                              setAddonForm((prev) => ({
                                ...prev,
                                price: e.target.value,
                              }))
                            }
                            placeholder="0.00"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>

                        {/* Form Actions */}
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            onClick={handleAddAddon}
                            disabled={
                              isSavingAddons ||
                              !addonForm.category ||
                              !addonForm.productService ||
                              !addonForm.startDate ||
                              !addonForm.endDate ||
                              !addonForm.price
                            }
                            className="flex items-center gap-2"
                          >
                            {isSavingAddons ? (
                              <Loader2Icon className="h-4 w-4 animate-spin" />
                            ) : (
                              "+"
                            )}
                            {isSavingAddons ? "Saving..." : "Save Add-on"}
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            onClick={() => {
                              setShowAddonForm(false);
                              setAddonForm({
                                category: "",
                                productService: "",
                                startDate: "",
                                endDate: "",
                                price: "",
                              });
                              setAddonProducts([]);
                            }}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    )}

                    {/* Add-ons List */}
                    {addons.length > 0 && (
                      <div className="mt-6">
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Service
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Qty
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Unit Price
                                </th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                  Total
                                </th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              {addons.map((addon) => (
                                <tr key={addon.id}>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    <div>
                                      <div className="font-medium">
                                        {addon.productServiceName}
                                      </div>
                                      <div className="text-gray-500">
                                        {addon.startDate} - {addon.endDate}
                                      </div>
                                    </div>
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {addon.quantity}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {activeFormatCurrency(addon.price)}
                                  </td>
                                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {activeFormatCurrency(addon.total)}
                                  </td>
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                        <div className="mt-4 text-right">
                          <div className="text-sm text-gray-600">
                            {addons.length} of {addons.length} results
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Packages Section - Only show if there are packages or if this is a new quote without opportunity, and inventory is not configured */}
              {shouldShowInventorySections() &&
                (packageItems.length > 0 ||
                  opportunityPackages.length > 0 ||
                  (id === "new" && !opportunityId)) && (
                  <SectionWrapper title="Packages">
                    <PackageLineItemsTable
                      packageItems={packageItems}
                      setPackageItems={setPackageItems}
                      onCalculateTotal={(updatedPackageItems) =>
                        calculateTotals(undefined, updatedPackageItems)
                      }
                      title="Packages"
                    />
                  </SectionWrapper>
                )}

              {/* Line Items Section - Only show if there are products or if this is a new quote without opportunity, and inventory is not configured */}
              {shouldShowInventorySections() &&
                (lineItems.length > 0 ||
                  opportunityProducts.length > 0 ||
                  (id === "new" && !opportunityId)) && (
                  <SectionWrapper title="Products">
                    <EnhancedLineItemsTable
                      lineItems={lineItems}
                      setLineItems={setLineItems}
                      onCalculateTotal={(updatedItems) =>
                        calculateTotals(updatedItems, undefined)
                      }
                      showUnits={true}
                      showDiscounts={true}
                      showListPrice={false}
                      showTaxRate={true}
                      title="Products"
                    />
                  </SectionWrapper>
                )}
            </div>

            {/* Right Column (4/12) - Quote summary and pricing */}
            <div className="lg:col-span-4 space-y-6">
              {/* Quote Summary Card */}
              <div className="bg-gray-50 rounded-lg p-6 border">
                <h3 className="text-lg font-semibold mb-4">Quote Summary</h3>

                {/* Tax Rates */}
                {shouldShowInventorySections() && (
                  <div className="space-y-4 mb-6">
                    <h4 className="font-medium text-sm text-gray-700">
                      Tax Rates
                    </h4>
                    <div className="grid grid-cols-1 gap-3">
                      <NumberFieldFormElement
                        control={form.control}
                        name="intraStateTaxRate"
                        label={`${countryConfig?.configuration?.primaryTaxName || "Intra-State Tax"} (%)`}
                        placeholder="0.00"
                        step={0.01}
                        min={0}
                        max={100}
                      />
                      <NumberFieldFormElement
                        control={form.control}
                        name="interStateTaxRate"
                        label={`${countryConfig?.configuration?.secondaryTaxName || "Inter-State Tax"} (%)`}
                        placeholder="0.00"
                        step={0.01}
                        min={0}
                        max={100}
                      />
                    </div>
                  </div>
                )}

                {shouldShowInventorySections() && (
                  <div className="space-y-4 mb-6">
                    <NumberFieldFormElement
                      control={form.control}
                      name="adjustments"
                      label={`Adjustments (${countryConfig?.configuration?.currencySymbol || "$"})`}
                      placeholder="0.00"
                      step={0.01}
                    />
                  </div>
                )}

                {/* Manual Grand Total Override */}
                <div className="space-y-4 mb-6">
                  <div className="flex items-center space-x-2">
                    <FormField
                      control={form.control}
                      name="isManualGrandTotal"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center space-x-2 space-y-0">
                          <FormControl>
                            <input
                              type="checkbox"
                              checked={field.value}
                              onChange={field.onChange}
                              className="h-4 w-4 rounded border-gray-300"
                            />
                          </FormControl>
                          <FormLabel className="text-sm font-medium">
                            Manual Grand Total
                          </FormLabel>
                        </FormItem>
                      )}
                    />
                  </div>
                  {form.watch("isManualGrandTotal") && (
                    <NumberFieldFormElement
                      control={form.control}
                      name="manualGrandTotal"
                      label={`Grand Total Override (${countryConfig?.configuration?.currencySymbol || "$"})`}
                      placeholder="0.00"
                      step={0.01}
                      min={0}
                    />
                  )}
                </div>

                {/* Totals Summary */}
                <div className="space-y-3 pt-4 border-t">
                  <div className="flex justify-between text-sm">
                    <span>Subtotal:</span>
                    <span className="font-medium">
                      {activeFormatCurrency(form.watch("subtotal") || 0)}
                    </span>
                  </div>
                  {shouldShowInventorySections() && (
                    <div className="flex justify-between text-sm">
                      <span>
                        {countryConfig?.configuration?.primaryTaxName || "Tax"}:
                      </span>
                      <span className="font-medium">
                        {activeFormatCurrency(form.watch("totalTax") || 0)}
                      </span>
                    </div>
                  )}

                  <div className="flex justify-between text-sm">
                    <span>Adjustments:</span>
                    <span className="font-medium">
                      {activeFormatCurrency(
                        safeParseNumber(form.watch("adjustments"), 0),
                      )}
                    </span>
                  </div>
                  <div className="flex justify-between text-lg font-bold border-t pt-2">
                    <span>Grand Total:</span>
                    <span>
                      {activeFormatCurrency(form.watch("grandTotal") || 0)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Advance Payment Section - Only show if cart exists */}
              {initialData.inventoryCartId ? (
                <div className="bg-white p-6 rounded-lg border">
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <CreditCard className="h-5 w-5" />
                    Advance Payment
                  </h3>

                  {!initialData.inventoryPaymentCollectionId ? (
                    <div className="space-y-3">
                      <p className="text-sm text-gray-600">
                        Initiate an advance payment to secure this quotation and
                        begin the booking process.
                      </p>
                      <Button
                        type="button"
                        onClick={() => setIsAdvancePaymentModalOpen(true)}
                        className="w-full bg-blue-600 hover:bg-blue-700"
                      >
                        <CreditCard className="mr-2 h-4 w-4" />
                        Initiate Advance Payment
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {/* Check if payment has been recorded */}
                      {initialData.paymentStatus === "recorded" ? (
                        // Payment Recorded State
                        <div className="space-y-3">
                          <div className="flex items-center gap-2 text-blue-600">
                            <CheckCircle className="h-5 w-5" />
                            <span className="font-medium">
                              Payment Recorded ✓
                            </span>
                          </div>
                          <div className="text-sm text-gray-600">
                            Payment Collection ID:{" "}
                            {initialData.inventoryPaymentCollectionId}
                          </div>
                          {initialData.paymentRecordedAt && (
                            <div className="text-sm text-gray-600">
                              Recorded on:{" "}
                              {new Date(
                                initialData.paymentRecordedAt,
                              ).toLocaleDateString()}{" "}
                              at{" "}
                              {new Date(
                                initialData.paymentRecordedAt,
                              ).toLocaleTimeString()}
                            </div>
                          )}
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                            <p className="text-sm text-blue-800">
                              Manual payment has been successfully recorded. The
                              advance payment is now complete.
                            </p>
                          </div>
                        </div>
                      ) : (
                        // Payment Initiated but Not Recorded State
                        <div className="space-y-3">
                          <div className="flex items-center gap-2 text-green-600">
                            <div className="w-2 h-2 bg-green-600 rounded-full"></div>
                            <span className="text-sm font-medium">
                              Payment Collection Initiated
                            </span>
                          </div>
                          <div className="text-xs text-gray-500">
                            Payment Collection ID:{" "}
                            {initialData.inventoryPaymentCollectionId}
                          </div>
                          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                            <p className="text-sm text-green-800">
                              Advance payment has been initiated. You can now
                              proceed with manual payment collection.
                            </p>
                          </div>

                          {/* Record Manual Payment Button */}
                          <Button
                            type="button"
                            onClick={() => setIsRecordPaymentModalOpen(true)}
                            className="w-full bg-green-600 hover:bg-green-700"
                          >
                            <Receipt className="mr-2 h-4 w-4" />
                            Record Manual Payment
                          </Button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <div className="bg-white rounded-lg p-6 border space-y-4">
                  <h3 className="text-lg font-semibold">Actions</h3>

                  <div className="space-y-3">
                    <EnhancedQuoteGeneratorButton
                      quote={{
                        quote_number: `QUO-${Date.now()}`,
                        date_issued: new Date().toISOString(),
                        valid_until: form.watch("validUntil")
                          ? new Date(
                              form.watch("validUntil") as string,
                            ).toISOString()
                          : new Date(
                              Date.now() + 30 * 24 * 60 * 60 * 1000,
                            ).toISOString(),
                        customer_name:
                          form.watch("quotationToBusinessName") || "Customer",
                        customer_address: "Customer Address",
                        payment_terms: form.watch("paymentTerms") || "Net 30",
                        // Always provide arrays for line_items and package_items
                        line_items:
                          Array.isArray(lineItems) && lineItems.length > 0
                            ? lineItems.map((item) => ({
                                description:
                                  item.description || "Service/Product",
                                quantity: item.quantity || 1,
                                unit_price: item.unitPrice || 0,
                                subtotal: item.subtotal || 0,
                                discount_type:
                                  item.discountType || "PERCENTAGE",
                                discount_value: item.discountValue || 0,
                                tax_rate: item.taxRate || 0,
                                is_manual_pricing:
                                  item.isManualPricing || false,
                                manual_line_total:
                                  item.manualLineTotal || undefined,
                                item_type: "PRODUCT" as const,
                                unit_type: item.unitType || undefined,
                              }))
                            : [],
                        package_items:
                          Array.isArray(packageItems) && packageItems.length > 0
                            ? packageItems.map((item) => ({
                                package_name: item.packageName || "Package",
                                description: item.description || "",
                                quantity: item.quantity || 1,
                                unit_price: item.unitPrice || 0,
                                subtotal: item.subtotal || 0,
                                discount_type:
                                  item.discountType || "PERCENTAGE",
                                discount_value: item.discountValue || 0,
                                tax_rate: item.taxRate || 0,
                                is_manual_pricing: false, // Packages don't use manual pricing in PDF
                                package_id: item.packageId || "",
                              }))
                            : [],
                        // Enhanced pricing fields
                        quote_discount_type: (form.watch("quoteDiscountType") ||
                          "PERCENTAGE") as "PERCENTAGE" | "FLAT",
                        quote_discount_value:
                          form.watch("quoteDiscountValue") || 0,
                        intra_state_tax_rate:
                          form.watch("intraStateTaxRate") || 0,
                        inter_state_tax_rate:
                          form.watch("interStateTaxRate") || 0,
                        adjustments: form.watch("adjustments") || 0,
                        is_manual_total: form.watch("isManualTotal") || false,
                        manual_total_value:
                          form.watch("manualTotalValue") || undefined,
                        is_manual_grand_total:
                          form.watch("isManualGrandTotal") || false,
                        manual_grand_total:
                          form.watch("manualGrandTotal") || undefined,
                        quotation_from: {
                          business_name:
                            form.watch("quotationFromBusinessName") ||
                            "Your Business",
                          address:
                            form.watch("quotationFromAddress") || undefined,
                          city: form.watch("quotationFromCity") || undefined,
                          state: form.watch("quotationFromState") || undefined,
                          postal_code:
                            form.watch("quotationFromPostalCode") || undefined,
                          country:
                            form.watch("quotationFromCountry") || undefined,
                          gstin: form.watch("quotationFromGSTIN") || undefined,
                        },
                        quotation_to: {
                          business_name:
                            form.watch("quotationToBusinessName") || "Customer",
                          address:
                            form.watch("quotationToAddress") || undefined,
                          city: form.watch("quotationToCity") || undefined,
                          state: form.watch("quotationToState") || undefined,
                          postal_code:
                            form.watch("quotationToPostalCode") || undefined,
                          country:
                            form.watch("quotationToCountry") || undefined,
                          gstin: form.watch("quotationToGSTIN") || undefined,
                        },
                      }}
                      templateId={templateId}
                      variant="download"
                      size="default"
                      className="w-full"
                      countryConfig={countryConfig?.configuration as any}
                      options={{
                        company_name:
                          organizationData?.data?.organization?.name ||
                          "Your Company",
                        company_logo:
                          organizationData?.data?.organization?.logo ||
                          undefined,
                        company_address:
                          form.watch("quotationFromAddress") ||
                          "Your Company Address",
                      }}
                    >
                      Generate PDF {templateId && `(${templateName})`}
                    </EnhancedQuoteGeneratorButton>

                    <Button
                      onClick={form.handleSubmit(onSubmit)}
                      disabled={isSavingFromHook}
                      className="w-full"
                    >
                      {isSavingFromHook ? (
                        <>
                          <Loader2Icon className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          {isEditMode ? "Update Quotation" : "Create Quotation"}
                        </>
                      )}
                    </Button>

                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => router.push("/quotes")}
                      className="w-full"
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </form>
      </Form>

      {/* Advance Payment Modal */}
      <AdvancePaymentModal
        isOpen={isAdvancePaymentModalOpen}
        onClose={() => setIsAdvancePaymentModalOpen(false)}
        onSuccess={handleAdvancePaymentSuccess}
        quotationId={id}
        currency="GBP"
        maxAmount={
          initialData && initialData.grandTotal
            ? parseFloat(initialData.grandTotal)
            : undefined
        }
      />

      {/* Record Payment Modal */}
      {initialData?.inventoryPaymentCollectionId && (
        <RecordPaymentModal
          isOpen={isRecordPaymentModalOpen}
          onClose={() => setIsRecordPaymentModalOpen(false)}
          onSuccess={handleRecordPaymentSuccess}
          quotationId={id}
          paymentCollectionId={initialData.inventoryPaymentCollectionId}
          currency="GBP"
          expectedAmount={
            initialData && initialData.grandTotal
              ? parseFloat(initialData.grandTotal)
              : undefined
          }
        />
      )}
    </div>
  );
}
