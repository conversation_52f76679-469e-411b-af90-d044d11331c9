import { useState } from "react";
import { toast } from "sonner";

interface TravellerInfo {
  primary_contact: {
    name: string;
    email: string;
    dob?: string;
    country?: string;
  };
  travellers: Array<{
    name: string;
    dob: string;
  }>;
}

interface SaveTravellerInfoParams {
  cartId: string;
  travellerInfo: TravellerInfo;
}

interface SaveTravellerInfoResult {
  success: boolean;
  message: string;
  error?: string;
}

export function useSaveTravellerInfo() {
  const [isSaving, setIsSaving] = useState(false);

  const saveTravellerInfo = async ({
    cartId,
    travellerInfo,
  }: SaveTravellerInfoParams): Promise<SaveTravellerInfoResult> => {
    setIsSaving(true);

    try {
      console.log("🔄 Saving traveller info to cart metadata:", {
        cartId,
        travellerInfo,
      });

      // Validate traveller info
      if (
        !travellerInfo.primary_contact.name ||
        !travellerInfo.primary_contact.email
      ) {
        throw new Error("Primary contact name and email are required");
      }

      // Validate email format
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(travellerInfo.primary_contact.email)) {
        throw new Error("Primary contact email must be valid");
      }

      // Validate traveller names and DOBs
      const travellers = travellerInfo.travellers || [];
      for (let i = 0; i < travellers.length; i++) {
        const traveller = travellers[i];
        if (!traveller.name) {
          throw new Error(`Traveller ${i + 1} name is required`);
        }
        if (!traveller.dob) {
          throw new Error(`Traveller ${i + 1} date of birth is required`);
        }

        // Validate DOB is not in future
        const dob = new Date(traveller.dob);
        const today = new Date();
        if (dob > today) {
          throw new Error(
            `Traveller ${i + 1} date of birth cannot be in the future`,
          );
        }
      }

      // Check for duplicates
      const allNames = [
        travellerInfo.primary_contact.name,
        ...travellers.map((t) => t.name),
      ];
      const allDOBs = [
        travellerInfo.primary_contact.dob,
        ...travellers.map((t) => t.dob),
      ];

      for (let i = 0; i < allNames.length; i++) {
        for (let j = i + 1; j < allNames.length; j++) {
          if (allNames[i] === allNames[j] && allDOBs[i] === allDOBs[j]) {
            throw new Error(
              "Duplicate traveller found with same name and date of birth",
            );
          }
        }
      }

      // Prepare metadata update
      const metadataUpdate = {
        metadata: {
          traveller_info: travellerInfo,
        },
      };

      // Call the inventory API to update cart metadata
      const response = await fetch(
        `/api/hotel-management/cart/${cartId}/metadata`,
        {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(metadataUpdate),
        },
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            `Failed to save traveller info: ${response.status}`,
        );
      }

      const result = await response.json();

      console.log("✅ Traveller info saved successfully:", result);

      toast.success("Traveller information saved successfully");

      return {
        success: true,
        message: "Traveller information saved successfully",
      };
    } catch (error) {
      console.error("❌ Error saving traveller info:", error);

      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to save traveller information";
      toast.error(errorMessage);

      return {
        success: false,
        message: "Failed to save traveller information",
        error: errorMessage,
      };
    } finally {
      setIsSaving(false);
    }
  };

  return {
    saveTravellerInfo,
    isSaving,
  };
}
