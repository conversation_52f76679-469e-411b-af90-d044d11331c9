import * as React from "react";
import { QuotesTable } from "./page-client";
import { getQuotes } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";
import { NoAccessCard } from "@flinkk/edge-case/no-access-card";

export default async function QuotesListPage(props: any) {
  const searchParams = await props.searchParams;

  const search = searchParamsCache.parse(searchParams);

  const validFilters = getValidFilters(search.filters ?? []);

  // Get quotes data with permissions
  const result = await getQuotes({
    ...search,
    filters: validFilters,
  });

  if (!Boolean(result?.permissions?.canView)) {
    return <NoAccessCard />;
  }

  if (result.data.length === 0 && Object.keys(searchParams).length === 0) {
    return (
      <NoDataCard
        title="No Quotations Found"
        description="Quotations are generated once an opportunity reaches the proposal stage. You’ll see them here as your deals progress."
      />
    );
  }

  return (
    <QuotesTable
      data={result.data}
      pageCount={result.pageCount}
      permissions={result.permissions}
    />
  );
}
