"use server";

import type { GetPriceBooksSchema } from "./_lib/validations";
import { getServerSession } from "@flinkk/shared-auth/server-session";
import { getModelPermissions } from "@flinkk/shared-rbac";
import { revalidatePath } from "next/cache";

// Helper function to fetch products
export async function getProducts(search?: string) {
  try {
    const { tenantId } = await getServerSession();

    if (!tenantId) {
      throw new Error("Unauthorized");
    }

    const { prisma } = await import("@flinkk/database/prisma");

    const whereClause: any = {
      tenantId,
      deleted: false,
    };

    if (search) {
      whereClause.OR = [
        { name: { contains: search, mode: "insensitive" } },
        { sku: { contains: search, mode: "insensitive" } },
        { description: { contains: search, mode: "insensitive" } },
      ];
    }

    const products = await prisma.product.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        sku: true,
        description: true,
        sellingPrice: true,
        type: true,
      },
      take: 50,
      orderBy: {
        name: "asc",
      },
    });

    return products.map((product: any) => ({
      value: product.id,
      label: product.name,
      sku: product.sku,
      description: product.description,
      price: product.sellingPrice,
      type: product.type,
    }));
  } catch (error) {
    console.error("Error fetching products:", error);
    return [];
  }
}

export async function getPriceBooks(input?: GetPriceBooksSchema) {
  // Get all permissions for price books model
  const permissions = await getModelPermissions("priceBook");

  // Check if user has permission to read price books
  if (!permissions.canView) {
    return {
      data: [],
      pageCount: 0,
      permissions: {
        canView: false,
        canCreate: false,
        canEdit: false,
        canDelete: false,
      },
    };
  }

  const whereConditions = [
    input?.name
      ? { name: { contains: input.name, mode: "insensitive" } }
      : undefined,
    input?.description
      ? { description: { contains: input.description, mode: "insensitive" } }
      : undefined,
    input?.status && Array.isArray(input.status) && input.status.length > 0
      ? { status: { in: input.status } }
      : undefined,
    input?.currency &&
    Array.isArray(input.currency) &&
    input.currency.length > 0
      ? { currency: { in: input.currency } }
      : undefined,
    input?.isDefault ? { isDefault: input.isDefault === "true" } : undefined,
    input?.productId
      ? {
          entries: {
            some: {
              productId: input.productId,
              isActive: true,
            },
          },
        }
      : undefined,
    input?.productName
      ? {
          entries: {
            some: {
              product: {
                name: { contains: input.productName, mode: "insensitive" },
              },
              isActive: true,
            },
          },
        }
      : undefined,
  ].filter(Boolean);

  // Use direct Prisma query since we need complex includes
  const { prisma } = await import("@flinkk/database/prisma");
  const { tenantId } = await getServerSession();

  // Ensure valid pagination values
  const page = Math.max(1, Number(input?.page) || 1);
  const perPage = Math.max(1, Number(input?.perPage) || 25);
  const skip = (page - 1) * perPage;

  // Build where conditions
  const where = {
    AND: [
      ...whereConditions,
      { tenantId: tenantId },
      { deleted: false },
    ].filter(Boolean),
  };

  // Build orderBy
  const orderBy = input?.sort?.length
    ? input.sort.map((item) => ({
        [item.id]: item.desc ? "desc" : "asc",
      }))
    : [
        { isDefault: "desc" }, // Default price books first
        { name: "asc" },
      ];

  // Execute query with transaction
  const [data, total] = await prisma.$transaction([
    prisma.priceBook.findMany({
      where,
      take: perPage,
      skip,
      orderBy,
      include: {
        entries: {
          where: {
            isActive: true,
          },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true,
                description: true,
                sellingPrice: true,
                costPrice: true,
              },
            },
          },
          take: 1, // Get the first entry for display
          orderBy: {
            createdAt: "asc",
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        updatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            entries: true,
          },
        },
      },
    }),
    prisma.priceBook.count({ where }),
  ]);

  // Transform the data to match the expected structure
  const transformedData = data.map((priceBook: any) => {
    const firstEntry = priceBook.entries?.[0];
    const product = firstEntry?.product;

    return {
      ...priceBook,
      // Add product information from the first entry
      productId: product?.id,
      productName: product?.name,
      productSku: product?.sku,
      basePrice: firstEntry?.basePrice,
      listPrice: firstEntry?.listPrice,
      costPrice: firstEntry?.costPrice,
      minimumQuantity: firstEntry?.minimumQuantity,
      maximumQuantity: firstEntry?.maximumQuantity,
    };
  });

  // Return both data and permissions for client-side use
  return {
    data: transformedData,
    pageCount: Math.ceil(total / perPage),
    permissions,
  };
}

export async function getPriceBookById(id: string) {
  try {
    // Get all permissions for price books model
    const permissions = await getModelPermissions("priceBook");

    // Check if user has permission to view price book details
    if (!permissions.canView) {
      throw new Error("You do not have permission to view price book details.");
    }

    const { tenantId } = await getServerSession();

    if (!tenantId) {
      throw new Error("Unauthorized");
    }

    const { prisma } = await import("@flinkk/database/prisma");

    const priceBook = await prisma.priceBook.findFirst({
      where: {
        id,
        tenantId,
        deleted: false,
      },
      include: {
        entries: {
          where: {
            isActive: true,
          },
          include: {
            product: {
              select: {
                id: true,
                name: true,
                description: true,
                sku: true,
                sellingPrice: true,
                costPrice: true,
                type: true,
                unit: {
                  select: {
                    id: true,
                    name: true,
                    displayName: true,
                  },
                },
              },
            },
          },
          orderBy: {
            product: {
              name: "asc",
            },
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        updatedBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            entries: {
              where: {
                isActive: true,
              },
            },
          },
        },
      },
    });

    return priceBook;
  } catch (error) {
    console.error("Error fetching price book:", error);
    throw error;
  }
}

export async function createPriceBook(data: {
  productId: string;
  name: string;
  description?: string;
  basePrice: number;
  listPrice?: number;
  costPrice?: number;
  currency?: string;
  effectiveFrom?: string;
  effectiveTo?: string;
  isDefault?: boolean;
  status?: string;
  minimumQuantity?: number;
  maximumQuantity?: number;
}) {
  try {
    // Get all permissions for price books model
    const permissions = await getModelPermissions("priceBook");

    // Check if user has permission to create price books
    if (!permissions.canCreate) {
      throw new Error("You do not have permission to create price books.");
    }

    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    const { prisma } = await import("@flinkk/database/prisma");

    // If setting as default, unset other default price books
    if (data.isDefault) {
      await prisma.priceBook.updateMany({
        where: {
          tenantId,
          isDefault: true,
          deleted: false,
        },
        data: {
          isDefault: false,
          updatedById: userId,
        },
      });
    }

    // Create the price book
    console.log("Available Prisma models:", Object.keys(prisma));
    const priceBook = await prisma.priceBook.create({
      data: {
        name: data.name.trim(),
        description: data.description?.trim() || null,
        currency: data.currency || "USD",
        effectiveFrom: data.effectiveFrom ? new Date(data.effectiveFrom) : null,
        effectiveTo: data.effectiveTo ? new Date(data.effectiveTo) : null,
        isDefault: data.isDefault || false,
        status: (data.status as any) || "ACTIVE",
        tenantId,
        createdById: userId,
        updatedById: userId,
      },
    });

    // Create the price book entry for the selected product
    await prisma.priceBookEntry.create({
      data: {
        priceBookId: priceBook.id,
        productId: data.productId,
        basePrice: data.basePrice,
        listPrice: data.listPrice || null,
        costPrice: data.costPrice || null,
        minimumQuantity: data.minimumQuantity || null,
        maximumQuantity: data.maximumQuantity || null,
        effectiveFrom: data.effectiveFrom ? new Date(data.effectiveFrom) : null,
        effectiveTo: data.effectiveTo ? new Date(data.effectiveTo) : null,
        tenantId,
        createdById: userId,
        updatedById: userId,
      },
    });

    // Revalidate the price books list page to show the new price book
    revalidatePath("/price-books");

    return priceBook;
  } catch (error) {
    console.error("Error creating price book:", error);
    throw error;
  }
}

export async function updatePriceBook(
  id: string,
  data: {
    name?: string;
    description?: string;
    currency?: string;
    effectiveFrom?: string;
    effectiveTo?: string;
    isDefault?: boolean;
    status?: string;
  },
) {
  try {
    // Get all permissions for price books model
    const permissions = await getModelPermissions("priceBook");

    // Check if user has permission to update price books
    if (!permissions.canEdit) {
      throw new Error("You do not have permission to update price books.");
    }

    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    const { prisma } = await import("@flinkk/database/prisma");

    // Check if price book exists
    const existingPriceBook = await prisma.priceBook.findFirst({
      where: { id, tenantId, deleted: false },
    });

    if (!existingPriceBook) {
      throw new Error("Price book not found");
    }

    // If setting as default, unset other default price books
    if (data.isDefault && !existingPriceBook.isDefault) {
      await prisma.priceBook.updateMany({
        where: {
          tenantId,
          isDefault: true,
          deleted: false,
          id: { not: id },
        },
        data: {
          isDefault: false,
          updatedById: userId,
        },
      });
    }

    const updatedPriceBook = await prisma.priceBook.update({
      where: { id, tenantId },
      data: {
        ...(data.name && { name: data.name.trim() }),
        ...(data.description !== undefined && {
          description: data.description?.trim() || null,
        }),
        ...(data.currency && { currency: data.currency }),
        ...(data.effectiveFrom !== undefined && {
          effectiveFrom: data.effectiveFrom
            ? new Date(data.effectiveFrom)
            : null,
        }),
        ...(data.effectiveTo !== undefined && {
          effectiveTo: data.effectiveTo ? new Date(data.effectiveTo) : null,
        }),
        ...(data.isDefault !== undefined && { isDefault: data.isDefault }),
        ...(data.status && { status: data.status as any }),
        updatedById: userId,
      },
    });

    // Revalidate the price books list page to show the updated price book
    revalidatePath("/price-books");

    return updatedPriceBook;
  } catch (error) {
    console.error("Error updating price book:", error);
    throw error;
  }
}

export async function deletePriceBook(id: string) {
  try {
    // Get all permissions for price books model
    const permissions = await getModelPermissions("priceBook");

    // Check if user has permission to delete price books
    if (!permissions.canDelete) {
      throw new Error("You do not have permission to delete price books.");
    }

    const { tenantId, userId } = await getServerSession();

    if (!tenantId || !userId) {
      throw new Error("Unauthorized");
    }

    const { prisma } = await import("@flinkk/database/prisma");

    // Check if price book exists
    const priceBook = await prisma.priceBook.findFirst({
      where: { id, tenantId, deleted: false },
    });

    if (!priceBook) {
      throw new Error("Price book not found");
    }

    // Prevent deletion of default price book
    if (priceBook.isDefault) {
      throw new Error("Cannot delete the default price book");
    }

    // Soft delete the price book
    await prisma.priceBook.update({
      where: { id, tenantId },
      data: {
        deleted: true,
        deletedAt: new Date(),
        updatedById: userId,
      },
    });

    // Revalidate the price books list page to remove the deleted price book
    revalidatePath("/price-books");

    return { success: true };
  } catch (error) {
    console.error("Error deleting price book:", error);
    throw error;
  }
}
