"use client";

import type { PriceBook } from "@/types/price-book";

import * as React from "react";

import { DataTable } from "@flinkk/data-table/component/data-table";
import { useDataTable } from "@flinkk/data-table/hooks/use-data-table";
import { DataTableSortList } from "@flinkk/data-table/component/data-table-sort-list";
import { DataTableToolbar } from "@flinkk/data-table/component/data-table-toolbar";
import { useDelete } from "@flinkk/hooks";
import { getPriceBooksTableColumns } from "./columns";
import { useRouter } from "next/navigation";
import dynamic from "next/dynamic";

const DeleteConfirmationDialog = dynamic(
  () =>
    import("@flinkk/patterns/model/delete-pop-up").then(
      (mod) => mod.DeleteConfirmationDialog
    ),
  {
    ssr: false,
  }
);

interface ModelPermissions {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
}

interface PriceBooksTableProps {
  data: any[];
  pageCount: number;
  permissions: ModelPermissions;
}

export function PriceBooksTable({
  data,
  pageCount,
  permissions,
}: PriceBooksTableProps) {
  const router = useRouter();

  const columns = React.useMemo(() => getPriceBooksTableColumns(), []);

  const { table } = useDataTable({
    data: data,
    columns,
    pageCount: pageCount,
    enableColumnFilters: true,
    getRowId: (originalRow) => originalRow.id,
    shallow: false,
    clearOnDefault: true,
  });

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false);
  const [priceBookToDelete, setPriceBookToDelete] =
    React.useState<PriceBook | null>(null);

  const { deleteRecord } = useDelete({
    model: "price-book",
    onSuccess: () => {
      setIsDeleteDialogOpen(false);
      setPriceBookToDelete(null);
      router.refresh();
    },
  });

  const handleView = React.useCallback(
    (priceBook: PriceBook) => {
      // Get price book name for the tab title
      const priceBookName = priceBook.name || "Unnamed Price Book";

      // Create URL with title parameter
      const url = `/price-books/${priceBook.id}/view?title=${encodeURIComponent(priceBookName)}`;
      router.push(url);
    },
    [router]
  );

  const handleEdit = React.useCallback(
    (priceBook: PriceBook) => {
      // Get price book name for the tab title
      const priceBookName = priceBook.name || "Unnamed Price Book";

      // Create URL with title parameter for edit page
      const url = `/price-books/${priceBook.id}?title=${encodeURIComponent(`Edit ${priceBookName}`)}`;
      router.push(url);
    },
    [router]
  );

  const handleDeletePriceBook = async () => {
    if (priceBookToDelete) {
      await deleteRecord(priceBookToDelete.id);
    }
  };

  // Add meta to table for action handlers
  table.options.meta = {
    onView: handleView,
    onEdit: handleEdit,
    onDelete: (priceBook: PriceBook) => {
      if (priceBook.isDefault) {
        return; // Prevent deletion of default price book
      }
      setPriceBookToDelete(priceBook);
      setIsDeleteDialogOpen(true);
    },
  };

  return (
    <div className="space-y-4">
      <DataTable
        table={table}
        onRowClick={(row) => {
          // Get price book name for the tab title
          const priceBookName = row.name || "Unnamed Price Book";

          // Create URL with title parameter
          const url = `/price-books/${row.id}/view?title=${encodeURIComponent(priceBookName)}`;
          router.push(url);
        }}
        doctype="price-book"
      >
        <DataTableToolbar
          table={table}
          buttonText={permissions.canCreate ? "New Price Book" : undefined}
          href={permissions.canCreate ? "/price-books/new" : undefined}
        >
          <DataTableSortList table={table} align="end" />
        </DataTableToolbar>
      </DataTable>

      <DeleteConfirmationDialog
        isOpen={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
        onDelete={handleDeletePriceBook}
        title="Delete Price Book"
        description={
          priceBookToDelete
            ? `Are you sure you want to delete "${priceBookToDelete.name}"? This action cannot be undone.`
            : "Are you sure you want to delete this price book? This action cannot be undone."
        }
      />
    </div>
  );
}
