"use client";

import type { ColumnDef } from "@tanstack/react-table";
import type { PriceBook } from "@/types/price-book";
import { Badge } from "@flinkk/components/ui/badge";
import { Button } from "@flinkk/components/ui/button";
import { Checkbox } from "@flinkk/components/ui/checkbox";
import { DataTableColumnHeader } from "@flinkk/data-table/component/data-table-column-header";
import { format } from "date-fns";
import { MoreHorizontal, Eye, Edit, Trash2, Check, X } from "lucide-react";
import { formatCurrency, toMinorUnits } from "@flinkk/money-math";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@flinkk/components/ui/dropdown-menu";

export function getPriceBooksTableColumns(): ColumnDef<PriceBook>[] {
  return [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Price Book Name" />
      ),
      cell: ({ row }) => {
        const priceBook = row.original;
        return (
          <div className="w-[220px]">
            <p className="font-medium">{priceBook.name}</p>
          </div>
        );
      },
      enableColumnFilter: true,
      meta: {
        label: "Name",
        variant: "text",
        placeholder: "Search by name...",
      },
      filterFn: (row, id, value) => {
        const name = row.getValue(id) as string;
        return name?.toLowerCase().includes(value.toLowerCase());
      },
    },
    {
      accessorKey: "productName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Product" />
      ),
      cell: ({ row }) => {
        const priceBook = row.original;
        return (
          <div className="flex flex-col w-[200px]">
            <span className="font-medium">
              {priceBook.productName || "Unknown Product"}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "productSku",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Product Code" />
      ),
      cell: ({ row }) => {
        const priceBook = row.original;
        return (
          <div className="flex flex-col w-[140px]">
            {priceBook.productSku && <span>{priceBook.productSku}</span>}
          </div>
        );
      },
    },
    {
      accessorKey: "description",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Description" />
      ),
      cell: ({ row }) => {
        const description = row.getValue("description") as string;
        return (
          <div className="max-w-[200px] truncate">{description || "—"}</div>
        );
      },
    },
    {
      accessorKey: "basePrice",
      header: ({ column }) => (
        <DataTableColumnHeader
          column={column}
          title="Base Price"
          className="w-[120px]"
        />
      ),
      cell: ({ row }) => {
        const priceBook = row.original;
        const basePrice = priceBook.basePrice;
        const currency = priceBook.currency || "USD";

        if (basePrice === undefined || basePrice === null) {
          return <span className="text-muted-foreground">—</span>;
        }

        return (
          <div className="flex flex-col">
            <span className="font-medium">
              {formatCurrency(toMinorUnits(basePrice, currency), currency)}
            </span>
            {priceBook.listPrice && priceBook.listPrice !== basePrice && (
              <span className="text-xs text-muted-foreground line-through">
                {formatCurrency(
                  toMinorUnits(priceBook.listPrice, currency),
                  currency,
                )}
              </span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        return (
          <Badge
            variant={
              status === "ACTIVE"
                ? "default"
                : status === "INACTIVE"
                  ? "secondary"
                  : status === "DRAFT"
                    ? "outline"
                    : "destructive"
            }
          >
            {status}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "isDefault",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Default" />
      ),
      cell: ({ row }) => {
        const priceBook = row.original;
        return (
          <div className="flex justify-center">
            {priceBook.isDefault ? (
              <Check className="h-4 w-4 text-green-600" />
            ) : (
              <X className="h-4 w-4 text-gray-400" />
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "effectiveFrom",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Effective From" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("effectiveFrom") as string;
        return date ? format(new Date(date), "MMM dd, yyyy") : "—";
      },
    },
    {
      accessorKey: "effectiveTo",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Effective To" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("effectiveTo") as string;
        return date ? format(new Date(date), "MMM dd, yyyy") : "—";
      },
    },
    {
      accessorKey: "currency",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Currency" />
      ),
      cell: ({ row }) => {
        const currency = row.getValue("currency") as string;
        return <span className="font-mono">{currency}</span>;
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created" />
      ),
      cell: ({ row }) => {
        const date = row.getValue("createdAt") as string;
        return format(new Date(date), "MMM dd, yyyy");
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row, table }) => {
        const priceBook = row.original;
        const meta = table.options.meta as any;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
              >
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">Open menu</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() => meta?.onView?.(priceBook)}
                className="cursor-pointer"
              >
                <Eye className="mr-2 h-4 w-4" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => meta?.onEdit?.(priceBook)}
                className="cursor-pointer"
              >
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() => meta?.onDelete?.(priceBook)}
                className="cursor-pointer text-destructive focus:text-destructive"
                disabled={priceBook.isDefault}
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];
}
