import * as React from "react";
import { PriceBooksTable } from "./page-client";
import { getPriceBooks } from "./actions";
import { searchParamsCache } from "./_lib/validations";
import { getValidFilters } from "@flinkk/data-table/lib/data-table";
import { NoDataCard } from "@flinkk/edge-case/no-data-card";
import { NoAccessCard } from "@flinkk/edge-case/no-access-card";

export default async function PriceBooksPage(props: any) {
  const searchParams = await props.searchParams;
  const search = searchParamsCache.parse(searchParams);
  const validFilters = getValidFilters(search.filters ?? []);

  const result = await getPriceBooks({
    ...search,
    filters: validFilters,
  });

  if (!Boolean(result?.permissions?.canView)) {
    return <NoAccessCard />;
  }

  if (result.data.length === 0 && Object.keys(searchParams).length === 0) {
    return (
      <NoDataCard
        title="No Packages Available"
        description="Packages help bundle products and services for streamlined quotations. Create a new package to start offering combined deals."
        href="/price-books/new"
        ctaName="Create Price Book"
      />
    );
  }

  return (
    <PriceBooksTable
      data={result.data}
      pageCount={result.pageCount}
      permissions={result.permissions}
    />
  );
}
