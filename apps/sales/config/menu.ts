export function getSidebarData(
  hasAgent: boolean = false,
  hasInventoryConfigured: boolean = false,
) {
  const baseItems = [
    {
      title: "Home",
      url: "/dashboard",
      icon: "Home",
      // No module key, always visible
    },
    {
      title: "My Leads",
      url: "/my-leads",
      icon: "UserPlus",
      // No module key, always visible
    },
  ];

  // Only add Bailey AI Chat if agent exists
  if (hasAgent) {
    baseItems.push({
      title: "Bailey AI Chat",
      url: "/agent-flow",
      icon: "BaileyAI",
    });
  }

  // Define Accounts items based on inventory configuration
  const accountsItems = [
    {
      title: "Contacts",
      url: "/contacts",
      icon: "UserCircle",
      module: "Contacts",
    },
  ];

  // Only add Companies if inventory is NOT configured (inverted logic)
  if (!hasInventoryConfigured) {
    accountsItems.unshift({
      title: "Companies",
      url: "/accounts",
      icon: "BuildingFill",
      module: "Companies",
    });
  }

  return [
    {
      title: "",
      items: baseItems,
    },
    {
      title: "Sales",
      items: [
        {
          title: "Leads",
          url: "/leads",
          icon: "Leads",
          module: "Leads",
        },
        {
          title: "Opportunities",
          url: "/opportunities",
          icon: "BriefcaseFill",
          module: "Opportunities",
        },
        {
          title: "Quotations",
          url: "/quotes",
          icon: "QuotationIcon",
          module: "Quotations",
        },
      ],
    },
    {
      title: "Accounts",
      items: accountsItems,
    },
    {
      title: "Activities",
      items: [
        {
          title: "Tasks",
          url: "/tasks",
          icon: "CheckSquare",
          module: "Tasks",
        },
        {
          title: "Events",
          url: "/events",
          icon: "Events",
          module: "Events",
        },
        // {
        //   title: "Emails",
        //   url: "/email",
        //   icon: "Mail",
        // },
      ],
    },
    // Only show Sales Tools if inventory is NOT configured
    ...(hasInventoryConfigured
      ? []
      : [
          {
            title: "Sales Tools",
            items: [
              {
                title: "Products",
                url: "/products",
                icon: "Package",
                module: "Products",
              },
              {
                title: "Price Books",
                url: "/price-books",
                icon: "DollarSign",
                module: "Price Books",
              },
              {
                title: "Packages",
                url: "/packages",
                icon: "PackageOpen",
                module: "Packages",
              },
              {
                title: "Campaigns",
                url: "/campaigns",
                icon: "Campaign",
                module: "Campaigns",
              },
            ],
          },
        ]),
  ];
}

// For backward compatibility, export a static version (without inventory check)
export const sidebarData = [
  {
    title: "",
    items: [
      {
        title: "Home",
        url: "/dashboard",
        icon: "Home",
        module: "Dashboard",
      },
    ],
  },
  {
    title: "Sales",
    items: [
      {
        title: "Leads",
        url: "/leads",
        icon: "Leads",
        module: "Leads",
      },
      {
        title: "Opportunities",
        url: "/opportunities",
        icon: "BriefcaseFill",
        module: "Opportunities",
      },
      {
        title: "Quotations",
        url: "/quotes",
        icon: "QuotationIcon",
        module: "Quotations",
      },
    ],
  },
  {
    title: "Accounts",
    items: [
      {
        title: "Companies",
        url: "/accounts",
        icon: "BuildingFill",
        module: "Companies",
      },
      {
        title: "Contacts",
        url: "/contacts",
        icon: "UserCircle",
        module: "Contacts",
      },
    ],
  },
  {
    title: "Activities",
    items: [
      {
        title: "Tasks",
        url: "/tasks",
        icon: "CheckSquare",
        module: "Tasks",
      },
      {
        title: "Events",
        url: "/events",
        icon: "Events",
        module: "Events",
      },
    ],
  },
  {
    title: "Sales Tools",
    items: [
      {
        title: "Products",
        url: "/products",
        icon: "Package",
        module: "Products",
      },
      {
        title: "Price Books",
        url: "/price-books",
        icon: "DollarSign",
        module: "Price Books",
      },
      {
        title: "Packages",
        url: "/packages",
        icon: "PackageOpen",
        module: "Packages",
      },
      {
        title: "Campaigns",
        url: "/campaigns",
        icon: "Campaign",
        module: "Campaigns",
      },
    ],
  },
];
