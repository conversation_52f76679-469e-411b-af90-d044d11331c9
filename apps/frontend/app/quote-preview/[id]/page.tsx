"use client";

import React, { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { Button } from "@flinkk/components/ui/button";
import { Download, Check, MessageSquare, ArrowLeft } from "lucide-react";
import Image from "next/image";
import { formatCurrency, toMinorUnits } from "@flinkk/money-math";

// Mock data - in real implementation, this would be fetched based on the ID
const mockQuoteData = {
  id: "quote_123",
  coverImage: "/images/alpine-hero-background.jpg",
  customSections: [],
  selectedItems: [
    {
      id: "1",
      name: "Swiss Alps - Zermatt - The Omnia - Deluxe Room with Matterhorn View",
      description:
        "Spacious room with floor-to-ceiling windows offering breathtaking Matterhorn views. Features contemporary design with luxury amenities.",
      imageUrl: "/images/rooms/omnia-deluxe.jpg",
      price: 800,
    },
    {
      id: "2",
      name: "Private Ski Instructor",
      description:
        "Professional ski instruction tailored to your skill level. Full-day private lessons with certified instructors.",
      imageUrl: "/images/addons/ski-instructor.jpg",
      price: 450,
    },
  ],
  totalPrice: 1250,
  status: "sent",
};

export default function QuotePreviewPage() {
  const params = useParams();
  const [quoteData, setQuoteData] = useState(mockQuoteData);
  const [isProcessing, setIsProcessing] = useState(false);
  const [currentStatus, setCurrentStatus] = useState(quoteData.status);

  const handleStatusUpdate = async (newStatus: string) => {
    setIsProcessing(true);

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1500));

    setCurrentStatus(newStatus);
    setQuoteData((prev) => ({ ...prev, status: newStatus }));
    setIsProcessing(false);

    // Show success message
    alert(`Quote ${newStatus} successfully!`);
  };

  const handleDownloadPDF = () => {
    console.log("Generating PDF for quote:", params.id);
    // In real implementation, this would generate and download a PDF
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header Hero Section */}
      <div className="relative h-[70vh] bg-slate-900 overflow-hidden">
        <Image
          src={quoteData.coverImage}
          alt="Cover Image"
          fill
          className="object-cover opacity-45"
          priority
        />
        <div className="absolute inset-0 bg-gradient-to-b from-slate-900/30 via-slate-900/50 to-slate-900/70" />
        <div className="absolute inset-0 flex flex-col items-center justify-center text-center text-white px-6">
          <div className="max-w-4xl">
            <div className="mb-8">
              <div className="inline-flex items-center space-x-3 mb-4">
                <div className="w-16 h-16 bg-white/10 backdrop-blur-sm rounded-lg flex items-center justify-center border border-white/20">
                  <span className="text-white font-bold text-xl">PB</span>
                </div>
                <div className="text-left">
                  <h1 className="text-2xl font-light tracking-wide">
                    POWDER BYRNE
                  </h1>
                  <p className="text-sm opacity-80 tracking-widest">
                    LUXURY MOUNTAIN EXPERIENCES
                  </p>
                </div>
              </div>
            </div>
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-light mb-6 tracking-wide">
              YOUR BESPOKE
              <br />
              <span className="font-normal">MOUNTAIN EXPERIENCE</span>
            </h2>
            <p className="text-lg sm:text-xl md:text-2xl font-light opacity-90 max-w-2xl mx-auto leading-relaxed px-4">
              A curated quotation crafted exclusively for you
            </p>
            <div className="mt-8 flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8 text-sm opacity-80">
              <div className="flex items-center space-x-2">
                <span>Quote ID: {params.id}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span>
                  Status: {currentStatus.replace("_", " ").toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Introduction Section */}
      <div className="max-w-6xl mx-auto px-4 md:px-6">
        <div className="py-12 md:py-16 text-center">
          <h3 className="text-2xl sm:text-3xl font-light text-slate-800 mb-6 px-4">
            A DEDICATED SERVICE FROM THE
            <br />
            <span className="font-normal">MOMENT YOU BOOK</span>
          </h3>
          <p className="text-base md:text-lg text-slate-600 max-w-3xl mx-auto leading-relaxed mb-8 px-4">
            Our team of mountain experts have carefully curated this bespoke
            experience, ensuring every detail reflects your preferences and
            exceeds your expectations. From arrival to departure, we orchestrate
            seamless luxury in the heart of the Swiss Alps.
          </p>
        </div>
      </div>

      {/* Selected Items Sections */}
      {quoteData.selectedItems.length > 0 && (
        <div className="space-y-8 lg:space-y-16">
          {quoteData.selectedItems.map((item, index) => (
            <div key={item.id} className="mb-8 lg:mb-16">
              {/* Desktop Layout */}
              <div
                className={`hidden lg:flex min-h-[70vh] w-full ${index % 2 === 1 ? "flex-row-reverse" : ""}`}
              >
                {/* Image Section - 65% width */}
                <div className="w-[65%] relative">
                  <Image
                    src={item.imageUrl}
                    alt={item.name}
                    fill
                    className="object-cover"
                  />
                </div>

                {/* Content Section - 35% width */}
                <div className="w-[35%] bg-white flex items-center justify-center p-8 lg:p-16">
                  <div className="max-w-lg">
                    <h3 className="text-2xl lg:text-3xl font-bold text-slate-900 mb-6 tracking-wide leading-tight">
                      {item.name.toUpperCase()}
                    </h3>
                    <p className="text-slate-600 leading-relaxed text-base lg:text-lg mb-4">
                      {item.description}
                    </p>
                    <div className="text-slate-800 font-semibold text-lg">
                      ${item.price.toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Mobile Layout */}
              <div className="lg:hidden px-4">
                <div className="relative h-[30vh] w-full mb-4 rounded-lg overflow-hidden">
                  <Image
                    src={item.imageUrl}
                    alt={item.name}
                    fill
                    className="object-cover"
                  />
                </div>

                <div className="bg-white rounded-lg p-4 shadow-lg mb-4">
                  <h3 className="text-lg font-bold text-slate-900 mb-2 tracking-wide leading-tight">
                    {item.name.toUpperCase()}
                  </h3>
                  <p className="text-slate-600 leading-relaxed text-sm mb-3">
                    {item.description}
                  </p>
                  <div className="text-slate-800 font-semibold text-base">
                    ${item.price.toFixed(2)}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pricing Summary */}
      <div className="bg-slate-50 py-16">
        <div className="max-w-4xl mx-auto px-4 md:px-6">
          <div className="bg-white rounded-lg shadow-lg p-8">
            <h3 className="text-2xl font-bold text-slate-900 mb-6 text-center">
              INVESTMENT SUMMARY
            </h3>

            <div className="space-y-4 mb-6">
              {quoteData.selectedItems.map((item) => (
                <div
                  key={item.id}
                  className="flex justify-between items-center py-2 border-b border-slate-200"
                >
                  <span className="text-slate-700">{item.name}</span>
                  <span className="font-semibold text-slate-900">
                    {formatCurrency(toMinorUnits(item.price, "USD"), "USD")}
                  </span>
                </div>
              ))}
            </div>

            <div className="border-t-2 border-slate-900 pt-4 mb-6">
              <div className="flex justify-between items-center">
                <span className="text-xl font-bold text-slate-900">
                  TOTAL INVESTMENT
                </span>
                <span className="text-2xl font-bold text-slate-900">
                  ${quoteData.totalPrice.toFixed(2)}
                </span>
              </div>
            </div>

            {/* Deposit Section */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <div className="text-center">
                <h4 className="text-lg font-bold text-green-900 mb-2">
                  TO CONTINUE, PAY THIS SMALL AMOUNT
                </h4>
                <div className="text-3xl font-bold text-green-900 mb-2">
                  {formatCurrency(
                    toMinorUnits(quoteData.totalPrice * 0.1, "USD"),
                    "USD",
                  )}
                </div>
                <p className="text-sm text-green-700 mb-4">
                  Secure your booking with just 10% deposit
                </p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="text-center">
                    <div className="font-semibold text-green-900">
                      Deposit (10%)
                    </div>
                    <div className="text-green-700">
                      {formatCurrency(
                        toMinorUnits(quoteData.totalPrice * 0.1, "USD"),
                        "USD",
                      )}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="font-semibold text-green-900">
                      Remaining Balance
                    </div>
                    <div className="text-green-700">
                      ${(quoteData.totalPrice * 0.9).toFixed(2)}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="py-8 sm:py-16 text-center px-4">
        <h3 className="text-xl sm:text-2xl lg:text-3xl font-light text-slate-800 mb-6 sm:mb-8 tracking-wide">
          SECURE YOUR EXPERIENCE
        </h3>
        <div className="max-w-sm sm:max-w-2xl mx-auto">
          <div className="flex flex-col gap-3 sm:gap-4 mb-6 sm:mb-8">
            <Button
              onClick={() => handleStatusUpdate("accepted")}
              disabled={isProcessing || currentStatus === "accepted"}
              className="w-full bg-slate-800 hover:bg-slate-700 text-white py-3 sm:py-4 text-base sm:text-lg font-light tracking-wide"
            >
              <Check className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
              {isProcessing && currentStatus !== "accepted"
                ? "PROCESSING..."
                : "APPROVE QUOTE"}
            </Button>
            <Button
              onClick={() => handleStatusUpdate("changes_requested")}
              disabled={isProcessing}
              variant="outline"
              className="w-full py-3 sm:py-4 text-base sm:text-lg font-light tracking-wide border-slate-300 bg-transparent"
            >
              <MessageSquare className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
              REQUEST CHANGES
            </Button>
          </div>
          <div className="flex flex-col gap-3 sm:gap-4">
            <Button
              onClick={() => handleStatusUpdate("rejected")}
              disabled={isProcessing}
              variant="outline"
              className="w-full py-2 sm:py-3 px-4 sm:px-6 text-sm sm:text-base font-light tracking-wide border-red-300 text-red-600 hover:bg-red-50"
            >
              DECLINE QUOTE
            </Button>
            <Button
              onClick={handleDownloadPDF}
              variant="outline"
              className="w-full py-2 sm:py-3 px-4 sm:px-6 text-sm sm:text-base font-light tracking-wide border-slate-300 bg-transparent"
            >
              <Download className="w-4 h-4 mr-2" />
              DOWNLOAD PDF
            </Button>
          </div>
        </div>
      </div>

      {/* Status Display */}
      {currentStatus !== "sent" && (
        <div className="fixed bottom-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
          <div className="flex items-center space-x-2">
            <div
              className={`w-3 h-3 rounded-full ${
                currentStatus === "accepted"
                  ? "bg-green-500"
                  : currentStatus === "rejected"
                    ? "bg-red-500"
                    : currentStatus === "changes_requested"
                      ? "bg-yellow-500"
                      : "bg-gray-500"
              }`}
            />
            <span className="text-sm font-medium capitalize">
              {currentStatus.replace("_", " ")}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
