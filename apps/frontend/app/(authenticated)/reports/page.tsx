"use client";

import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@flinkk/components/ui/card";
import { But<PERSON> } from "@flinkk/components/ui/button";
import { Input } from "@flinkk/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@flinkk/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@flinkk/components/ui/tabs";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend,
  LineChart,
  Line,
  AreaChart,
  Area,
  FunnelChart,
  Funnel,
  LabelList,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from "recharts";
import {
  DownloadIcon,
  RotateCwIcon,
  CalendarIcon,
  BarChartIcon,
  LineChartIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  TableIcon,
  ArrowUpDownIcon,
  FunnelIcon,
  RadarIcon,
  SearchIcon,
} from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@flinkk/components/ui/table";
import { useCurrencyFormatterV2 } from "@flinkk/shared-hooks/use-currency-formatter-v2";
import {
  formatCurrency as formatCurrencyMath,
  toMinorUnits,
} from "@flinkk/money-math";

export default function ReportsPage() {
  const [timeRange, setTimeRange] = useState("last30days");
  const [sorting, setSorting] = useState<{
    column: string;
    direction: "asc" | "desc";
  } | null>(null);
  const [tableFilter, setTableFilter] = useState("");

  // Sample data for charts
  const salesData = [
    { name: "Jan", value: 4000 },
    { name: "Feb", value: 3000 },
    { name: "Mar", value: 5000 },
    { name: "Apr", value: 4500 },
    { name: "May", value: 6000 },
    { name: "Jun", value: 5500 },
    { name: "Jul", value: 7000 },
    { name: "Aug", value: 6500 },
    { name: "Sep", value: 8000 },
    { name: "Oct", value: 7500 },
    { name: "Nov", value: 9000 },
    { name: "Dec", value: 8500 },
  ];

  const leadSourceData = [
    { name: "Website", value: 400 },
    { name: "Referral", value: 300 },
    { name: "Social", value: 300 },
    { name: "Email", value: 200 },
    { name: "Paid Ads", value: 150 },
  ];

  const ticketCategoryData = [
    { name: "Technical", value: 45 },
    { name: "Billing", value: 25 },
    { name: "Account", value: 15 },
    { name: "Feature Request", value: 10 },
    { name: "Other", value: 5 },
  ];

  const customerGrowthData = [
    { name: "Jan", value: 100 },
    { name: "Feb", value: 120 },
    { name: "Mar", value: 150 },
    { name: "Apr", value: 170 },
    { name: "May", value: 200 },
    { name: "Jun", value: 220 },
    { name: "Jul", value: 250 },
    { name: "Aug", value: 280 },
    { name: "Sep", value: 310 },
    { name: "Oct", value: 340 },
    { name: "Nov", value: 360 },
    { name: "Dec", value: 400 },
  ];

  const salesByProductData = [
    { name: "Product A", value: 35000 },
    { name: "Product B", value: 25000 },
    { name: "Product C", value: 18000 },
    { name: "Product D", value: 12000 },
    { name: "Product E", value: 10000 },
  ];

  const supportResponseTimeData = [
    { name: "Jan", value: 45 },
    { name: "Feb", value: 40 },
    { name: "Mar", value: 35 },
    { name: "Apr", value: 30 },
    { name: "May", value: 25 },
    { name: "Jun", value: 20 },
    { name: "Jul", value: 18 },
    { name: "Aug", value: 15 },
    { name: "Sep", value: 14 },
    { name: "Oct", value: 12 },
    { name: "Nov", value: 10 },
    { name: "Dec", value: 8 },
  ];

  // Sales funnel data
  const salesFunnelData = [
    { name: "Leads", value: 5000 },
    { name: "Qualified", value: 3500 },
    { name: "Proposals", value: 2200 },
    { name: "Negotiations", value: 1100 },
    { name: "Closed Won", value: 800 },
  ];

  // Lead conversion rate data
  const leadConversionData = [
    { name: "Jan", value: 12 },
    { name: "Feb", value: 14 },
    { name: "Mar", value: 13 },
    { name: "Apr", value: 15 },
    { name: "May", value: 18 },
    { name: "Jun", value: 17 },
    { name: "Jul", value: 20 },
    { name: "Aug", value: 22 },
    { name: "Sep", value: 21 },
    { name: "Oct", value: 23 },
    { name: "Nov", value: 25 },
    { name: "Dec", value: 28 },
  ];

  // Sales performance radar chart data
  const salesPerformanceData = [
    { subject: "New Business", A: 120, B: 110, fullMark: 150 },
    { subject: "Upselling", A: 98, B: 130, fullMark: 150 },
    { subject: "Retention", A: 86, B: 130, fullMark: 150 },
    { subject: "Referrals", A: 99, B: 100, fullMark: 150 },
    { subject: "Cross-selling", A: 85, B: 90, fullMark: 150 },
  ];

  // Detailed sales data for table
  const detailedSalesData = [
    {
      id: 1,
      customer: "Acme Inc.",
      product: "Product A",
      amount: 12500,
      date: "2023-12-15",
      rep: "Sarah Johnson",
    },
    {
      id: 2,
      customer: "TechCorp",
      product: "Product B",
      amount: 8750,
      date: "2023-12-10",
      rep: "Michael Brown",
    },
    {
      id: 3,
      customer: "GlobalTech",
      product: "Product A",
      amount: 15000,
      date: "2023-12-05",
      rep: "David Wilson",
    },
    {
      id: 4,
      customer: "Innovate Solutions",
      product: "Product C",
      amount: 6300,
      date: "2023-12-01",
      rep: "Sarah Johnson",
    },
    {
      id: 5,
      customer: "Future Systems",
      product: "Product D",
      amount: 4200,
      date: "2023-11-28",
      rep: "Emily Davis",
    },
    {
      id: 6,
      customer: "Acme Inc.",
      product: "Product E",
      amount: 3800,
      date: "2023-11-25",
      rep: "Michael Brown",
    },
    {
      id: 7,
      customer: "TechCorp",
      product: "Product C",
      amount: 7200,
      date: "2023-11-20",
      rep: "David Wilson",
    },
    {
      id: 8,
      customer: "GlobalTech",
      product: "Product B",
      amount: 9500,
      date: "2023-11-15",
      rep: "Emily Davis",
    },
    {
      id: 9,
      customer: "Innovate Solutions",
      product: "Product A",
      amount: 11200,
      date: "2023-11-10",
      rep: "Sarah Johnson",
    },
    {
      id: 10,
      customer: "Future Systems",
      product: "Product D",
      amount: 5100,
      date: "2023-11-05",
      rep: "Michael Brown",
    },
  ];

  const COLORS = [
    "var(--chart-1)",
    "var(--chart-2)",
    "var(--chart-3)",
    "var(--chart-4)",
    "var(--chart-5)",
  ];

  // Calculate metrics
  const totalSales = salesData.reduce((sum, item) => sum + item.value, 0);
  const lastMonthSales = salesData[salesData.length - 2].value;
  const currentMonthSales = salesData[salesData.length - 1].value;
  const salesGrowth =
    ((currentMonthSales - lastMonthSales) / lastMonthSales) * 100;

  const totalCustomers =
    customerGrowthData[customerGrowthData.length - 1].value;
  const lastMonthCustomers =
    customerGrowthData[customerGrowthData.length - 2].value;
  const customerGrowth =
    ((totalCustomers - lastMonthCustomers) / lastMonthCustomers) * 100;

  const averageResponseTime =
    supportResponseTimeData[supportResponseTimeData.length - 1].value;
  const lastMonthResponseTime =
    supportResponseTimeData[supportResponseTimeData.length - 2].value;
  const responseTimeImprovement =
    ((lastMonthResponseTime - averageResponseTime) / lastMonthResponseTime) *
    100;

  // Dynamic currency formatter
  const { formatCurrency, isLoading: currencyLoading } =
    useCurrencyFormatterV2();

  // Format currency with fallback using @flinkk/money-math
  const formatCurrencyWithFallback = (value: number) => {
    if (currencyLoading) {
      // Use money-math for fallback formatting
      const minorUnits = toMinorUnits(value, "USD");
      return formatCurrencyMath(minorUnits, "USD", { compact: true });
    }
    return formatCurrency(value);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Handle sorting for the data table
  const handleSort = (column: string) => {
    if (sorting && sorting.column === column) {
      setSorting({
        column,
        direction: sorting.direction === "asc" ? "desc" : "asc",
      });
    } else {
      setSorting({ column, direction: "asc" });
    }
  };

  // Filter and sort the detailed sales data
  const filteredAndSortedSalesData = detailedSalesData
    .filter((item) => {
      if (!tableFilter) return true;
      const searchTerm = tableFilter.toLowerCase();
      return (
        item.customer.toLowerCase().includes(searchTerm) ||
        item.product.toLowerCase().includes(searchTerm) ||
        item.rep.toLowerCase().includes(searchTerm) ||
        formatCurrencyWithFallback(item.amount)
          .toLowerCase()
          .includes(searchTerm) ||
        formatDate(item.date).toLowerCase().includes(searchTerm)
      );
    })
    .sort((a, b) => {
      if (!sorting) return 0;

      let aValue: any = a[sorting.column as keyof typeof a];
      let bValue: any = b[sorting.column as keyof typeof b];

      if (typeof aValue === "string" && typeof bValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sorting.direction === "asc" ? -1 : 1;
      if (aValue > bValue) return sorting.direction === "asc" ? 1 : -1;
      return 0;
    });

  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Reports & Analytics</h1>
        <div className="flex items-center gap-2">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <CalendarIcon className="mr-2 h-4 w-4" />
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="last7days">Last 7 days</SelectItem>
              <SelectItem value="last30days">Last 30 days</SelectItem>
              <SelectItem value="last90days">Last 90 days</SelectItem>
              <SelectItem value="thisYear">This year</SelectItem>
              <SelectItem value="allTime">All time</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <RotateCwIcon className="h-4 w-4" />
          </Button>
          <Button>
            <DownloadIcon className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-muted-foreground">Total Sales</p>
                <p className="text-2xl font-bold">
                  {formatCurrencyWithFallback(totalSales)}
                </p>
              </div>
              <div
                className={`flex items-center ${
                  salesGrowth >= 0 ? "text-green-500" : "text-red-500"
                }`}
              >
                {salesGrowth >= 0 ? (
                  <ArrowUpIcon className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 mr-1" />
                )}
                <span>{Math.abs(salesGrowth).toFixed(1)}%</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Compared to last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-muted-foreground">Total Customers</p>
                <p className="text-2xl font-bold">{totalCustomers}</p>
              </div>
              <div
                className={`flex items-center ${
                  customerGrowth >= 0 ? "text-green-500" : "text-red-500"
                }`}
              >
                {customerGrowth >= 0 ? (
                  <ArrowUpIcon className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 mr-1" />
                )}
                <span>{Math.abs(customerGrowth).toFixed(1)}%</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Compared to last month
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="pt-6">
            <div className="flex justify-between items-start">
              <div>
                <p className="text-sm text-muted-foreground">
                  Avg. Response Time
                </p>
                <p className="text-2xl font-bold">{averageResponseTime} min</p>
              </div>
              <div
                className={`flex items-center ${
                  responseTimeImprovement >= 0
                    ? "text-green-500"
                    : "text-red-500"
                }`}
              >
                {responseTimeImprovement >= 0 ? (
                  <ArrowUpIcon className="h-4 w-4 mr-1" />
                ) : (
                  <ArrowDownIcon className="h-4 w-4 mr-1" />
                )}
                <span>{Math.abs(responseTimeImprovement).toFixed(1)}%</span>
              </div>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Improvement from last month
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="sales">
        <TabsList className="mb-4">
          <TabsTrigger value="sales">
            <BarChartIcon className="h-4 w-4 mr-2" />
            Sales
          </TabsTrigger>
          <TabsTrigger value="customers">
            <LineChartIcon className="h-4 w-4 mr-2" />
            Customers
          </TabsTrigger>
          <TabsTrigger value="support">
            <LineChartIcon className="h-4 w-4 mr-2" />
            Support
          </TabsTrigger>
          <TabsTrigger value="funnel">
            <FunnelIcon className="h-4 w-4 mr-2" />
            Sales Funnel
          </TabsTrigger>
          <TabsTrigger value="performance">
            <RadarIcon className="h-4 w-4 mr-2" />
            Performance
          </TabsTrigger>
          <TabsTrigger value="data">
            <TableIcon className="h-4 w-4 mr-2" />
            Data Table
          </TabsTrigger>
        </TabsList>

        <TabsContent value="sales">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Card>
              <CardHeader>
                <CardTitle>Monthly Sales</CardTitle>
                <CardDescription>
                  Sales performance over the last 12 months
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={salesData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip
                        formatter={(value) =>
                          formatCurrencyWithFallback(value as number)
                        }
                      />
                      <Bar dataKey="value" fill="var(--chart-1)" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Sales by Product</CardTitle>
                <CardDescription>
                  Distribution of sales by product
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={salesByProductData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) =>
                          `${name} ${(percent * 100).toFixed(0)}%`
                        }
                      >
                        {salesByProductData.map((_: any, index: number) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value) =>
                          formatCurrencyWithFallback(value as number)
                        }
                      />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="customers">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Card>
              <CardHeader>
                <CardTitle>Customer Growth</CardTitle>
                <CardDescription>
                  Customer acquisition over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={customerGrowthData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Area
                        type="monotone"
                        dataKey="value"
                        stroke="var(--chart-2)"
                        fill="var(--chart-2)"
                        fillOpacity={0.3}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lead Sources</CardTitle>
                <CardDescription>
                  Distribution of lead acquisition channels
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={leadSourceData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) =>
                          `${name} ${(percent * 100).toFixed(0)}%`
                        }
                      >
                        {leadSourceData.map((_: any, index: number) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="support">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Card>
              <CardHeader>
                <CardTitle>Support Response Time</CardTitle>
                <CardDescription>
                  Average response time in minutes
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={supportResponseTimeData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => `${value} min`} />
                      <Line
                        type="monotone"
                        dataKey="value"
                        stroke="var(--chart-3)"
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Ticket Categories</CardTitle>
                <CardDescription>
                  Distribution of support tickets by category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={ticketCategoryData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) =>
                          `${name} ${(percent * 100).toFixed(0)}%`
                        }
                      >
                        {ticketCategoryData.map((_: any, index: number) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="funnel">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Card>
              <CardHeader>
                <CardTitle>Sales Funnel</CardTitle>
                <CardDescription>
                  Conversion stages from leads to closed deals
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <FunnelChart>
                      <Tooltip
                        formatter={(value) =>
                          formatCurrencyWithFallback(value as number)
                        }
                      />
                      <Funnel
                        dataKey="value"
                        data={salesFunnelData}
                        isAnimationActive
                      >
                        <LabelList
                          position="right"
                          fill="#000"
                          stroke="none"
                          dataKey="name"
                        />
                        <LabelList
                          position="right"
                          fill="#000"
                          stroke="none"
                          dataKey="value"
                          content={({ value }) =>
                            formatCurrencyWithFallback(value as number)
                          }
                          offset={60}
                        />
                        {salesFunnelData.map((_: any, index: number) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={COLORS[index % COLORS.length]}
                          />
                        ))}
                      </Funnel>
                    </FunnelChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lead Conversion Rate</CardTitle>
                <CardDescription>
                  Monthly lead to customer conversion percentage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart data={leadConversionData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Line
                        type="monotone"
                        dataKey="value"
                        stroke="var(--chart-4)"
                        strokeWidth={2}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            <Card>
              <CardHeader>
                <CardTitle>Sales Performance</CardTitle>
                <CardDescription>
                  Performance across different sales activities
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <RadarChart
                      cx="50%"
                      cy="50%"
                      outerRadius="80%"
                      data={salesPerformanceData}
                    >
                      <PolarGrid />
                      <PolarAngleAxis dataKey="subject" />
                      <PolarRadiusAxis angle={30} domain={[0, 150]} />
                      <Radar
                        name="This Year"
                        dataKey="A"
                        stroke="var(--chart-1)"
                        fill="var(--chart-1)"
                        fillOpacity={0.6}
                      />
                      <Radar
                        name="Last Year"
                        dataKey="B"
                        stroke="var(--chart-2)"
                        fill="var(--chart-2)"
                        fillOpacity={0.6}
                      />
                      <Legend />
                      <Tooltip />
                    </RadarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Conversion Efficiency</CardTitle>
                <CardDescription>
                  Percentage of leads converted at each stage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-80">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={[
                        { name: "Lead to Qualified", value: 70 },
                        { name: "Qualified to Proposal", value: 63 },
                        { name: "Proposal to Negotiation", value: 50 },
                        { name: "Negotiation to Closed", value: 73 },
                      ]}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip formatter={(value) => `${value}%`} />
                      <Bar dataKey="value" fill="var(--chart-5)">
                        <LabelList
                          dataKey="value"
                          position="top"
                          formatter={(value: any) => `${value}%`}
                        />
                      </Bar>
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="data">
          <Card>
            <CardHeader>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                <div>
                  <CardTitle>Detailed Sales Data</CardTitle>
                  <CardDescription>
                    Comprehensive view of recent sales transactions
                  </CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <div className="relative">
                    <SearchIcon className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search transactions..."
                      className="pl-8 w-[250px]"
                      value={tableFilter}
                      onChange={(e) => setTableFilter(e.target.value)}
                    />
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setTableFilter("")}
                  >
                    Clear
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[50px]">ID</TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("customer")}
                      >
                        <div className="flex items-center">
                          Customer
                          {sorting?.column === "customer" && (
                            <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                          )}
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("product")}
                      >
                        <div className="flex items-center">
                          Product
                          {sorting?.column === "product" && (
                            <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                          )}
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer text-right"
                        onClick={() => handleSort("amount")}
                      >
                        <div className="flex items-center justify-end">
                          Amount
                          {sorting?.column === "amount" && (
                            <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                          )}
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("date")}
                      >
                        <div className="flex items-center">
                          Date
                          {sorting?.column === "date" && (
                            <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                          )}
                        </div>
                      </TableHead>
                      <TableHead
                        className="cursor-pointer"
                        onClick={() => handleSort("rep")}
                      >
                        <div className="flex items-center">
                          Sales Rep
                          {sorting?.column === "rep" && (
                            <ArrowUpDownIcon className="ml-2 h-4 w-4" />
                          )}
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredAndSortedSalesData.length > 0 ? (
                      filteredAndSortedSalesData.map((sale) => (
                        <TableRow key={sale.id}>
                          <TableCell>{sale.id}</TableCell>
                          <TableCell>{sale.customer}</TableCell>
                          <TableCell>{sale.product}</TableCell>
                          <TableCell className="text-right">
                            {formatCurrencyWithFallback(sale.amount)}
                          </TableCell>
                          <TableCell>{formatDate(sale.date)}</TableCell>
                          <TableCell>{sale.rep}</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={6} className="h-24 text-center">
                          No results found.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
