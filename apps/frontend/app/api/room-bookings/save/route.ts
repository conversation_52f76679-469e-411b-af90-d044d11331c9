import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { withTenantRBAC } from "@flinkk/shared-rbac";
import {
  FlinkkInventoryAPI,
  CartMetadataUpdateRequest,
} from "@flinkk/inventory-api";
import { toMinorUnits } from "@flinkk/money-math";

interface RoomBookingItem {
  variant_id: string;
  quantity: number;
  title: string;
  unit_price: number;
  currency: string; // Add currency field
  // Product-related fields at top level
  product_collection: string;
  product_description: string;
  product_handle: string;
  product_id: string;
  product_subtitle: string;
  product_title: string;
  product_type: string;
  product_type_id: string;
  raw_compare_at_unit_price: number;
  metadata: {
    room_id: string;
    hotel_id: string;
    hotel_name: string;
    start_date: string;
    end_date: string;
    room_config_id: string;
    room_config_name: string;
    number_of_rooms: number;
    // Product-related fields also in metadata for consistency
    product_collection: string;
    product_description: string;
    product_handle: string;
    product_id: string;
    product_subtitle: string;
    product_title: string;
    product_type: string;
    product_type_id: string;
    raw_compare_at_unit_price: number;
    // Occupancy and meal plan fields
    occupancy_type_id?: string;
    occupancy_type_name?: string;
    meal_plan_id?: string;
    meal_plan_name?: string;
    // Destination fields (optional)
    destination_id?: string;
    destination_name?: string;
  };
}

interface SaveRoomBookingsRequest {
  cartId: string;
  items: RoomBookingItem[];
  // Additional fields for cart metadata sync
  destination_id?: string;
  destination_name?: string;
  hotel_id?: string;
  hotel_name?: string;
  check_in_date?: string;
  check_out_date?: string;
}

/**
 * POST /api/room-bookings/save - Save room bookings to inventory cart and sync cart metadata
 */
export const POST = withTenantRBAC(
  async (req: NextRequest, rbacPrisma: any, tenantId: string) => {
    try {
      const { userId } = await getToken({ req });

      if (!userId || !tenantId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const body: SaveRoomBookingsRequest = await req.json();
      const {
        cartId,
        items,
        destination_id,
        destination_name,
        hotel_id,
        hotel_name,
        check_in_date,
        check_out_date,
      } = body;

      // Validate required fields
      if (!cartId) {
        return NextResponse.json(
          { error: "Cart ID is required" },
          { status: 400 },
        );
      }

      if (!items || !Array.isArray(items) || items.length === 0) {
        return NextResponse.json(
          { error: "Items array is required and cannot be empty" },
          { status: 400 },
        );
      }

      // Validate and convert monetary values to minor units
      const processedItems = items.map((item) => {
        // Validate currency is provided
        if (!item.currency) {
          throw new Error(`Currency is required for item ${item.variant_id}`);
        }

        // Convert unit_price to minor units
        const unitPriceMinor = toMinorUnits(item.unit_price, item.currency);

        // Convert raw_compare_at_unit_price to minor units if it exists
        const rawCompareAtUnitPriceMinor = item.raw_compare_at_unit_price
          ? toMinorUnits(item.raw_compare_at_unit_price, item.currency)
          : undefined;

        return {
          ...item,
          unit_price: unitPriceMinor,
          ...(rawCompareAtUnitPriceMinor !== undefined && {
            raw_compare_at_unit_price: rawCompareAtUnitPriceMinor,
          }),
        };
      });

      // Get inventory configuration for the tenant
      const inventoryConfig =
        await rbacPrisma.inventoryConfiguration.findUnique({
          where: {
            tenantId: tenantId,
          },
          select: {
            apiUrl: true,
            token: true,
            isActive: true,
            verificationStatus: true,
          },
        });

      // Check if inventory is configured and active
      if (
        !inventoryConfig ||
        !inventoryConfig.apiUrl ||
        !inventoryConfig.token
      ) {
        return NextResponse.json(
          { error: "Inventory system not configured for this tenant" },
          { status: 400 },
        );
      }

      if (!inventoryConfig.isActive) {
        return NextResponse.json(
          { error: "Inventory system is not active" },
          { status: 400 },
        );
      }

      // Create inventory API instance
      const inventoryAPI = new FlinkkInventoryAPI({
        apiUrl: inventoryConfig.apiUrl,
        token: inventoryConfig.token,
      });

      // Step 1: Update cart metadata with destination and hotel information
      let cartMetadataUpdated = false;
      let cartMetadataError = null;

      // Extract metadata from the first item if not provided in request body
      const firstItem = processedItems[0];
      const metadataToUpdate: CartMetadataUpdateRequest = {
        metadata: {
          destination_id: destination_id || firstItem?.metadata?.destination_id,
          destination_name:
            destination_name || firstItem?.metadata?.destination_name,
          hotel_id: hotel_id || firstItem?.metadata?.hotel_id,
          hotel_name: hotel_name || firstItem?.metadata?.hotel_name,
          check_in_date: check_in_date || firstItem?.metadata?.start_date,
          check_out_date: check_out_date || firstItem?.metadata?.end_date,
        },
      };

      // Only update metadata if we have at least some hotel information
      if (
        metadataToUpdate.metadata.hotel_id ||
        metadataToUpdate.metadata.hotel_name
      ) {
        try {
          console.log("🔄 Updating cart metadata:", metadataToUpdate);

          await inventoryAPI.updateCartMetadata(cartId, metadataToUpdate);

          cartMetadataUpdated = true;
          console.log("✅ Cart metadata updated successfully");
        } catch (error) {
          cartMetadataError = error;
          console.error("❌ Failed to update cart metadata:", error);

          // Log error details but don't fail the entire operation
          // This ensures the room booking save still succeeds even if metadata update fails
        }
      } else {
        console.log(
          "ℹ️ Skipping cart metadata update - no hotel information available",
        );
      }

      console.log("🛒 Adding items to cart via wrapper API:", {
        cartId,
        items: processedItems,
      });

      // Step 2: Call the inventory API to add items to cart
      const result = await inventoryAPI.addItemsToCart(cartId, processedItems);

      console.log("🎉 Inventory API call successful:", result);

      return NextResponse.json({
        success: true,
        message: `${processedItems.length} room booking${processedItems.length !== 1 ? "s" : ""} added to cart`,
        data: result,
        cartMetadataUpdated,
        cartMetadataError:
          cartMetadataError instanceof Error
            ? cartMetadataError.message
            : String(cartMetadataError),
      });
    } catch (error) {
      console.error("Error saving room bookings:", error);

      // Handle specific inventory API errors
      if (error instanceof Error) {
        if (error.message.includes("404")) {
          return NextResponse.json(
            { error: "Cart not found" },
            { status: 404 },
          );
        }
        if (error.message.includes("400")) {
          return NextResponse.json(
            { error: "Invalid request data" },
            { status: 400 },
          );
        }
      }

      return NextResponse.json(
        { error: "Failed to save room bookings" },
        { status: 500 },
      );
    }
  },
);
