import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";
import { z } from "zod";

export const dynamic = "force-dynamic";

// Validation schema for updating a family
const updateFamilySchema = z.object({
  name: z.string().min(1, "Family name is required"),
});

// PUT /api/crm/families/[id] - Update family name
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { tenantId, userId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const familyId = resolvedParams.id;

    const body = await req.json();
    const validatedData = updateFamilySchema.parse(body);

    // Verify the family exists and belongs to the tenant
    const existingFamily = await prisma.family.findFirst({
      where: {
        id: familyId,
        tenantId: tenantId,
      },
    });

    if (!existingFamily) {
      return NextResponse.json(
        { error: "Family not found" },
        { status: 404 }
      );
    }

    // Update the family
    const updatedFamily = await prisma.family.update({
      where: {
        id: familyId,
      },
      data: {
        name: validatedData.name,
        updatedById: userId,
      },
      include: {
        members: true,
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedFamily,
    });
  } catch (error) {
    console.error("Error updating family:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update family" },
      { status: 500 }
    );
  }
}

// DELETE /api/crm/families/[id] - Delete family and all members
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { tenantId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const familyId = resolvedParams.id;

    // Verify the family exists and belongs to the tenant
    const existingFamily = await prisma.family.findFirst({
      where: {
        id: familyId,
        tenantId: tenantId,
      },
    });

    if (!existingFamily) {
      return NextResponse.json(
        { error: "Family not found" },
        { status: 404 }
      );
    }

    // Delete the family (this will cascade delete all family members)
    await prisma.family.delete({
      where: {
        id: familyId,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Family and all members deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting family:", error);

    return NextResponse.json(
      { error: "Failed to delete family" },
      { status: 500 }
    );
  }
}
