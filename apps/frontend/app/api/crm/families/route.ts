import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";
import { z } from "zod";

export const dynamic = "force-dynamic";

// Validation schema for creating a family
const createFamilySchema = z.object({
  name: z.string().min(1, "Family name is required"),
  contactId: z.string().min(1, "Contact ID is required"),
});

// Validation schema for updating a family
const updateFamilySchema = z.object({
  name: z.string().min(1, "Family name is required"),
});

// POST /api/crm/families - Create a new family group
export async function POST(req: NextRequest) {
  try {
    const { tenantId, userId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const validatedData = createFamilySchema.parse(body);

    // Verify the contact exists and belongs to the tenant
    const contact = await prisma.contact.findFirst({
      where: {
        id: validatedData.contactId,
        tenantId: tenantId,
      },
    });

    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    // Create the family
    const family = await prisma.family.create({
      data: {
        name: validatedData.name,
        contactId: validatedData.contactId,
        tenantId: tenantId,
      },
      include: {
        members: true,
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: family,
    });
  } catch (error) {
    console.error("Error creating family:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create family" },
      { status: 500 }
    );
  }
}
