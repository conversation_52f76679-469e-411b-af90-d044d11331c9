import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";
import { z } from "zod";
import { FamilyRelationship } from "@prisma/client";

export const dynamic = "force-dynamic";

// Validation schema for updating a family member
const updateFamilyMemberSchema = z.object({
  name: z.string().min(1, "Name is required"),
  dob: z.string().refine((date) => {
    const parsedDate = new Date(date);
    const today = new Date();
    return parsedDate <= today;
  }, "Date of birth must be in the past"),
  relationship: z.nativeEnum(FamilyRelationship),
  notes: z.string().optional(),
});

// PUT /api/crm/family-members/[id] - Update family member
export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { tenantId, userId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const memberId = resolvedParams.id;

    const body = await req.json();
    const validatedData = updateFamilyMemberSchema.parse(body);

    // Verify the family member exists and belongs to the tenant
    const existingMember = await prisma.familyMember.findFirst({
      where: {
        id: memberId,
        tenantId: tenantId,
      },
    });

    if (!existingMember) {
      return NextResponse.json(
        { error: "Family member not found" },
        { status: 404 }
      );
    }

    // Update the family member
    const updatedMember = await prisma.familyMember.update({
      where: {
        id: memberId,
      },
      data: {
        name: validatedData.name,
        dob: new Date(validatedData.dob),
        relationship: validatedData.relationship,
        notes: validatedData.notes,
        updatedById: userId,
      },
      include: {
        family: {
          select: {
            id: true,
            name: true,
            contactId: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedMember,
    });
  } catch (error) {
    console.error("Error updating family member:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update family member" },
      { status: 500 }
    );
  }
}

// DELETE /api/crm/family-members/[id] - Delete family member
export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { tenantId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const memberId = resolvedParams.id;

    // Verify the family member exists and belongs to the tenant
    const existingMember = await prisma.familyMember.findFirst({
      where: {
        id: memberId,
        tenantId: tenantId,
      },
    });

    if (!existingMember) {
      return NextResponse.json(
        { error: "Family member not found" },
        { status: 404 }
      );
    }

    // Delete the family member
    await prisma.familyMember.delete({
      where: {
        id: memberId,
      },
    });

    return NextResponse.json({
      success: true,
      message: "Family member deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting family member:", error);

    return NextResponse.json(
      { error: "Failed to delete family member" },
      { status: 500 }
    );
  }
}
