import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";
import { z } from "zod";
import { FamilyRelationship } from "@prisma/client";

export const dynamic = "force-dynamic";

// Validation schema for creating a family member
const createFamilyMemberSchema = z.object({
  name: z.string().min(1, "Name is required"),
  dob: z.string().refine((date) => {
    const parsedDate = new Date(date);
    const today = new Date();
    return parsedDate <= today;
  }, "Date of birth must be in the past"),
  relationship: z.nativeEnum(FamilyRelationship),
  notes: z.string().optional(),
  familyId: z.string().min(1, "Family ID is required"),
});

// POST /api/crm/family-members - Add a family member
export async function POST(req: NextRequest) {
  try {
    const { tenantId, userId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();
    const validatedData = createFamilyMemberSchema.parse(body);

    // Verify the family exists and belongs to the tenant
    const family = await prisma.family.findFirst({
      where: {
        id: validatedData.familyId,
        tenantId: tenantId,
      },
    });

    if (!family) {
      return NextResponse.json(
        { error: "Family not found" },
        { status: 404 }
      );
    }

    // Check if family already has 10 members (max limit)
    const memberCount = await prisma.familyMember.count({
      where: {
        familyId: validatedData.familyId,
        tenantId: tenantId,
      },
    });

    if (memberCount >= 10) {
      return NextResponse.json(
        { error: "Maximum of 10 family members allowed per family" },
        { status: 400 }
      );
    }

    // Create the family member
    const familyMember = await prisma.familyMember.create({
      data: {
        name: validatedData.name,
        dob: new Date(validatedData.dob),
        relationship: validatedData.relationship,
        notes: validatedData.notes,
        familyId: validatedData.familyId,
        tenantId: tenantId,
      },
      include: {
        family: {
          select: {
            id: true,
            name: true,
            contactId: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      data: familyMember,
    });
  } catch (error) {
    console.error("Error creating family member:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create family member" },
      { status: 500 }
    );
  }
}
