import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { prisma } from "@flinkk/database/prisma";

export const dynamic = "force-dynamic";

// GET /api/crm/contacts/[id]/families - Get families for a contact
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { tenantId } = await getToken({ req });

    if (!tenantId) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const resolvedParams = await params;
    const contactId = resolvedParams.id;

    // Verify the contact exists and belongs to the tenant
    const contact = await prisma.contact.findFirst({
      where: {
        id: contactId,
        tenantId: tenantId,
      },
    });

    if (!contact) {
      return NextResponse.json(
        { error: "Contact not found" },
        { status: 404 }
      );
    }

    // Get families for this contact with their members
    const families = await prisma.family.findMany({
      where: {
        contactId: contactId,
        tenantId: tenantId,
      },
      include: {
        members: {
          orderBy: {
            createdAt: "asc",
          },
        },
        contact: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        createdBy: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return NextResponse.json({
      success: true,
      data: families,
      count: families.length,
    });
  } catch (error) {
    console.error("Error fetching families:", error);

    return NextResponse.json(
      { error: "Failed to fetch families" },
      { status: 500 }
    );
  }
}
