import { NextRequest, NextResponse } from "next/server";
import { getToken } from "@flinkk/shared-auth/token";
import { withTenantRBAC } from "@flinkk/shared-rbac";
import { FlinkkInventoryAPI } from "@flinkk/inventory-api";
import { toMinorUnits } from "@flinkk/money-math";

interface AddonItem {
  variant_id: string;
  product_id: string;
  quantity: number;
  title: string;
  unit_price: number;
  currency: string; // Add currency field
  metadata: {
    category_id: string;
    product_service_id: string;
    cost_price: number;
    type?: string;
    start_date: string;
    end_date: string;
  };
}

interface SaveAddonRequest {
  cartId: string;
  items: AddonItem[];
}

/**
 * POST /api/room-addon-add/save - Save room addons to inventory cart
 */
export const POST = withTenantRBAC(
  async (req: NextRequest, rbacPrisma: any, tenantId: string) => {
    try {
      const { userId } = await getToken({ req });

      if (!userId || !tenantId) {
        return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
      }

      const body: SaveAddonRequest = await req.json();
      const { cartId, items } = body;

      // Validate required fields
      if (!cartId) {
        return NextResponse.json(
          { error: "Cart ID is required" },
          { status: 400 },
        );
      }

      if (!items || !Array.isArray(items) || items.length === 0) {
        return NextResponse.json(
          { error: "Items array is required and cannot be empty" },
          { status: 400 },
        );
      }

      // Validate and convert monetary values to minor units
      const processedItems = items.map((item) => {
        // Validate currency is provided
        if (!item.currency) {
          throw new Error(`Currency is required for item ${item.variant_id}`);
        }

        // Convert unit_price to minor units
        const unitPriceMinor = toMinorUnits(item.unit_price, item.currency);

        // Convert cost_price to minor units if it exists
        const costPriceMinor = item.metadata.cost_price
          ? toMinorUnits(item.metadata.cost_price, item.currency)
          : undefined;

        return {
          ...item,
          unit_price: unitPriceMinor,
          metadata: {
            ...item.metadata,
            ...(costPriceMinor !== undefined && {
              cost_price: costPriceMinor,
            }),
          },
        };
      });

      // Validate each item has required fields
      for (const item of processedItems) {
        if (
          !item.variant_id ||
          !item.title ||
          !item.unit_price ||
          !item.metadata
        ) {
          return NextResponse.json(
            {
              error:
                "Each item must have variant_id, title, unit_price, and metadata",
            },
            { status: 400 },
          );
        }

        const { metadata } = item;
        if (
          !metadata.category_id ||
          !metadata.product_service_id ||
          !metadata.start_date ||
          !metadata.end_date
        ) {
          return NextResponse.json(
            {
              error:
                "Item metadata must include product_id, category_id, product_service_id, start_date, and end_date",
            },
            { status: 400 },
          );
        }
      }

      // Get inventory configuration for the tenant
      const inventoryConfig =
        await rbacPrisma.inventoryConfiguration.findUnique({
          where: {
            tenantId: tenantId,
          },
          select: {
            apiUrl: true,
            token: true,
            isActive: true,
            verificationStatus: true,
          },
        });

      // Check if inventory is configured and active
      if (
        !inventoryConfig ||
        !inventoryConfig.apiUrl ||
        !inventoryConfig.token
      ) {
        return NextResponse.json(
          { error: "Inventory system not configured for this tenant" },
          { status: 400 },
        );
      }

      if (!inventoryConfig.isActive) {
        return NextResponse.json(
          { error: "Inventory system is not active" },
          { status: 400 },
        );
      }

      // Create inventory API instance
      const inventoryAPI = new FlinkkInventoryAPI({
        apiUrl: inventoryConfig.apiUrl,
        token: inventoryConfig.token,
      });

      console.log("🛒 Adding addon items to cart via wrapper API:", {
        cartId,
        items: processedItems,
      });

      // Call the inventory API to add items to cart
      const result = await inventoryAPI.addItemsToCart(cartId, processedItems);

      console.log("🎉 Inventory API call successful:", result);

      return NextResponse.json({
        success: true,
        message: `${processedItems.length} addon${processedItems.length !== 1 ? "s" : ""} added to cart`,
        data: result,
      });
    } catch (error) {
      console.error("Error saving room addons:", error);

      // Handle specific inventory API errors
      if (error instanceof Error) {
        if (error.message.includes("404")) {
          return NextResponse.json(
            { error: "Cart not found" },
            { status: 404 },
          );
        }
        if (error.message.includes("400")) {
          return NextResponse.json(
            { error: "Invalid request data" },
            { status: 400 },
          );
        }
      }

      return NextResponse.json(
        { error: "Failed to save room addons" },
        { status: 500 },
      );
    }
  },
);
