import { getCurrencyMeta } from "./currency-map";
import { toMajorUnits } from "./convert";

export interface FormatOptions {
  /** Whether to show the currency symbol (default: true) */
  showSymbol?: boolean;
  /** Whether to show the currency code (default: false) */
  showCode?: boolean;
  /** Whether to use symbol only or include code (default: true) */
  useSymbolOnly?: boolean;
  /** Custom locale for number formatting (default: 'en-US') */
  locale?: string;
  /** Whether to use compact notation for large numbers (default: false) */
  compact?: boolean;
}

/**
 * Format currency amount from minor units into locale- and currency-aware string
 *
 * @param minorValue - The amount in minor units as an integer
 * @param currency - The currency code (e.g., "USD", "GBP")
 * @param options - Formatting options
 * @returns Formatted currency string
 *
 * @example
 * formatCurrency(10050, "USD") // "$100.50"
 * formatCurrency(10050, "USD", { locale: "en-GB" }) // "$100.50"
 * formatCurrency(10000, "INR", { locale: "en-IN" }) // "₹100.00"
 * formatCurrency(123, "JPY") // "¥123"
 * formatCurrency(10050, "USD", { showCode: true, useSymbolOnly: false }) // "USD 100.50"
 */
export function formatCurrency(
  minorValue: number,
  currency: string,
  options: FormatOptions = {},
): string {
  // Handle null, undefined, or NaN values
  if (minorValue === null || minorValue === undefined || isNaN(minorValue)) {
    return "N/A";
  }

  const {
    showSymbol = true,
    showCode = false,
    useSymbolOnly = true,
    locale = "en-US",
    compact = false,
  } = options;

  const meta = getCurrencyMeta(currency);
  const majorValue = parseFloat(toMajorUnits(minorValue, currency));

  // Use Intl.NumberFormat for locale-aware formatting
  const formatOptions: Intl.NumberFormatOptions = {
    style: "currency",
    currency: currency.toUpperCase(),
    minimumFractionDigits: meta.decimals,
    maximumFractionDigits: meta.decimals,
  };

  if (compact) {
    formatOptions.notation = "compact";
  }

  try {
    // Try using Intl.NumberFormat with the currency
    const formatter = new Intl.NumberFormat(locale, formatOptions);
    let formatted = formatter.format(majorValue);

    // Handle display preferences
    if (!showSymbol && !showCode) {
      // Remove both symbol and code, return just the number
      formatted = formatted.replace(/[^\d.,\s-]/g, "").trim();
    } else if (!showSymbol && showCode) {
      // Remove symbol and add currency code
      const numberPart = formatted.replace(/[^\d.,\s-]/g, "").trim();
      formatted = `${numberPart} ${currency.toUpperCase()}`;
    } else if (showCode && !useSymbolOnly) {
      // Replace symbol with currency code
      formatted = formatted.replace(meta.symbol, currency.toUpperCase());
    } else if (showCode && useSymbolOnly) {
      // Add currency code after symbol
      formatted = `${formatted} ${currency.toUpperCase()}`;
    }

    return formatted;
  } catch (error) {
    // Fallback to manual formatting if Intl.NumberFormat fails
    return formatCurrencyFallback(minorValue, currency, options);
  }
}

/**
 * Fallback currency formatting when Intl.NumberFormat is not available or fails
 */
function formatCurrencyFallback(
  minorValue: number,
  currency: string,
  options: FormatOptions = {},
): string {
  const { showSymbol = true, showCode = false, useSymbolOnly = true } = options;

  const meta = getCurrencyMeta(currency);
  const majorValue = toMajorUnits(minorValue, currency);

  // Add thousands separators
  const parts = majorValue.split(".");
  const integerPart = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  const decimalPart = parts[1] || "";

  let formattedNumber =
    meta.decimals > 0 ? `${integerPart}.${decimalPart}` : integerPart;

  // Handle symbol and code display
  let result = formattedNumber;

  if (showSymbol && showCode && useSymbolOnly) {
    result = `${meta.symbol}${formattedNumber} ${currency.toUpperCase()}`;
  } else if (showSymbol && !showCode) {
    result = `${meta.symbol}${formattedNumber}`;
  } else if (!showSymbol && showCode) {
    result = `${currency.toUpperCase()} ${formattedNumber}`;
  } else if (showCode && !useSymbolOnly) {
    result = `${currency.toUpperCase()} ${formattedNumber}`;
  }

  return result;
}

/**
 * Format currency for display in tables or compact spaces
 *
 * @param minorValue - The amount in minor units
 * @param currency - The currency code
 * @returns Compact formatted string
 */
export function formatCurrencyCompact(
  minorValue: number,
  currency: string,
): string {
  return formatCurrency(minorValue, currency, { compact: true });
}

/**
 * Format currency without symbol (numbers only)
 *
 * @param minorValue - The amount in minor units
 * @param currency - The currency code
 * @param locale - Optional locale for number formatting
 * @returns Formatted number string without currency symbol
 */
export function formatCurrencyNumber(
  minorValue: number,
  currency: string,
  locale?: string,
): string {
  return formatCurrency(minorValue, currency, {
    showSymbol: false,
    showCode: false,
    locale,
  });
}

/**
 * Format currency with currency code instead of symbol
 *
 * @param minorValue - The amount in minor units
 * @param currency - The currency code
 * @param locale - Optional locale for number formatting
 * @returns Formatted string with currency code
 */
export function formatCurrencyWithCode(
  minorValue: number,
  currency: string,
  locale?: string,
): string {
  return formatCurrency(minorValue, currency, {
    showSymbol: false,
    showCode: true,
    locale,
  });
}
