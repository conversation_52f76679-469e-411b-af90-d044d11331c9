/**
 * @flinkk/money-math - Monetary value utility for safe currency handling
 *
 * This package provides utilities for handling monetary values using minor units
 * (cents/pence) to eliminate floating-point precision issues.
 */

// Export currency metadata utilities
export {
  currencyMeta,
  getCurrencyMeta,
  isSupportedCurrency,
  type CurrencyMeta,
} from "./currency-map";

// Export conversion utilities
export {
  toMinorUnits,
  toMajorUnits,
  addMinorUnits,
  subtractMinorUnits,
  multiplyMinorUnits,
  divideMinorUnits,
  calculatePercentage,
} from "./convert";

// Export formatting utilities
export {
  formatCurrency,
  formatCurrencyCompact,
  formatCurrencyNumber,
  formatCurrencyWithCode,
  type FormatOptions,
} from "./format";

// Re-export types from @flinkk/currency for convenience
export type { CurrencyCodes } from "@flinkk/currency";
