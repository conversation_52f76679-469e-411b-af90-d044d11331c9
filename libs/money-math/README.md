# @flinkk/money-math

A utility package for safe monetary value handling using minor units (cents/pence) to eliminate floating-point precision issues.

## Features

- ✅ Store all money values as integers (minor units) in databases and APIs
- ✅ Convert safely between display format (major units) and storage format (minor units)
- ✅ Support formatting by currency and locale
- ✅ Build a reusable utility with a testable and documented interface
- ✅ Avoid duplication of currency logic across projects

## Installation

```bash
npm install @flinkk/money-math
```

## Usage

### Converting between major and minor units

```typescript
import { toMinorUnits, toMajorUnits } from "@flinkk/money-math";

// Convert display value to storage value
const minorUnits = toMinorUnits("100.50", "USD"); // 10050
const minorUnits2 = toMinorUnits(100.5, "USD"); // 10050

// Convert storage value to display value
const majorUnits = toMajorUnits(10050, "USD"); // "100.50"
```

### Formatting currency for display

```typescript
import { formatCurrency } from "@flinkk/money-math";

// Format minor units for display
const formatted = formatCurrency(10050, "USD"); // "$100.50"
const formatted2 = formatCurrency(10050, "USD", "en-GB"); // "$100.50"
const formatted3 = formatCurrency(10000, "INR", "en-IN"); // "₹100.00"
```

### Supported Currencies

- USD (2 decimal places)
- GBP (2 decimal places)
- EUR (2 decimal places)
- INR (2 decimal places)
- JPY (0 decimal places)
- And more...

## API Reference

### `toMinorUnits(amount: number | string, currency: string): number`

Converts a decimal currency value to minor unit integer.

### `toMajorUnits(minorValue: number, currency: string): string`

Converts a stored integer (minor units) to decimal string.

### `formatCurrency(minorValue: number, currency: string, locale?: string): string`

Formats integer (minor units) into locale- and currency-aware string.

## Integration with @flinkk/currency

This package uses `@flinkk/currency` for currency symbols and metadata.
