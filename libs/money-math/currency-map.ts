import { getCurrencySymbol } from "@flinkk/currency";

export interface CurrencyMeta {
  decimals: number;
  symbol: string;
}

/**
 * Currency metadata map with decimal places and symbols
 * Uses @flinkk/currency for symbol lookup
 */
export const currencyMeta: Record<string, CurrencyMeta> = {
  USD: { decimals: 2, symbol: getCurrencySymbol("USD") },
  GBP: { decimals: 2, symbol: getCurrencySymbol("GBP") },
  INR: { decimals: 2, symbol: getCurrencySymbol("INR") },
  JPY: { decimals: 0, symbol: getCurrencySymbol("JPY") },
  EUR: { decimals: 2, symbol: getCurrencySymbol("EUR") },
  CAD: { decimals: 2, symbol: getCurrencySymbol("CAD") },
  AUD: { decimals: 2, symbol: getCurrencySymbol("AUD") },
  BRL: { decimals: 2, symbol: getCurrencySymbol("BRL") },
  CHF: { decimals: 2, symbol: getCurrencySymbol("CHF") },
  CNY: { decimals: 2, symbol: getCurrencySymbol("CNY") },
  KRW: { decimals: 0, symbol: getCurrencySymbol("KRW") },
  SGD: { decimals: 2, symbol: getCurrencySymbol("SGD") },
  HKD: { decimals: 2, symbol: getCurrencySymbol("HKD") },
  NOK: { decimals: 2, symbol: getCurrencySymbol("NOK") },
  SEK: { decimals: 2, symbol: getCurrencySymbol("SEK") },
  DKK: { decimals: 2, symbol: getCurrencySymbol("DKK") },
  PLN: { decimals: 2, symbol: getCurrencySymbol("PLN") },
  CZK: { decimals: 2, symbol: getCurrencySymbol("CZK") },
  HUF: { decimals: 2, symbol: getCurrencySymbol("HUF") },
  RUB: { decimals: 2, symbol: getCurrencySymbol("RUB") },
  TRY: { decimals: 2, symbol: getCurrencySymbol("TRY") },
  ZAR: { decimals: 2, symbol: getCurrencySymbol("ZAR") },
  MXN: { decimals: 2, symbol: getCurrencySymbol("MXN") },
  ARS: { decimals: 2, symbol: getCurrencySymbol("ARS") },
  CLP: { decimals: 0, symbol: getCurrencySymbol("CLP") },
  COP: { decimals: 2, symbol: getCurrencySymbol("COP") },
  PEN: { decimals: 2, symbol: getCurrencySymbol("PEN") },
  UYU: { decimals: 2, symbol: getCurrencySymbol("UYU") },
  EGP: { decimals: 2, symbol: getCurrencySymbol("EGP") },
  MAD: { decimals: 2, symbol: getCurrencySymbol("MAD") },
  NGN: { decimals: 2, symbol: getCurrencySymbol("NGN") },
  KES: { decimals: 2, symbol: getCurrencySymbol("KES") },
  GHS: { decimals: 2, symbol: getCurrencySymbol("GHS") },
  THB: { decimals: 2, symbol: getCurrencySymbol("THB") },
  VND: { decimals: 0, symbol: getCurrencySymbol("VND") },
  IDR: { decimals: 2, symbol: getCurrencySymbol("IDR") },
  MYR: { decimals: 2, symbol: getCurrencySymbol("MYR") },
  PHP: { decimals: 2, symbol: getCurrencySymbol("PHP") },
  PKR: { decimals: 2, symbol: getCurrencySymbol("PKR") },
  BDT: { decimals: 2, symbol: getCurrencySymbol("BDT") },
  LKR: { decimals: 2, symbol: getCurrencySymbol("LKR") },
  NPR: { decimals: 2, symbol: getCurrencySymbol("NPR") },
  AED: { decimals: 2, symbol: getCurrencySymbol("AED") },
  SAR: { decimals: 2, symbol: getCurrencySymbol("SAR") },
  QAR: { decimals: 2, symbol: getCurrencySymbol("QAR") },
  KWD: { decimals: 3, symbol: getCurrencySymbol("KWD") },
  BHD: { decimals: 3, symbol: getCurrencySymbol("BHD") },
  OMR: { decimals: 3, symbol: getCurrencySymbol("OMR") },
  JOD: { decimals: 3, symbol: getCurrencySymbol("JOD") },
  ILS: { decimals: 2, symbol: getCurrencySymbol("ILS") },
};

/**
 * Get currency metadata for a given currency code
 */
export function getCurrencyMeta(currencyCode: string): CurrencyMeta {
  const meta = currencyMeta[currencyCode.toUpperCase()];
  if (!meta) {
    throw new Error(`Unsupported currency: ${currencyCode}`);
  }
  return meta;
}

/**
 * Check if a currency is supported
 */
export function isSupportedCurrency(currencyCode: string): boolean {
  return currencyCode.toUpperCase() in currencyMeta;
}
