import { getCurrencyMeta } from "./currency-map";

/**
 * Convert a decimal currency value (major units) to minor unit integer
 *
 * @param amount - The amount in major units (e.g., "100.50" or 100.50)
 * @param currency - The currency code (e.g., "USD", "GBP")
 * @returns The amount in minor units as an integer (e.g., 10050 for $100.50)
 *
 * @example
 * toMinorUnits("100.50", "USD") // 10050
 * toMinorUnits(100.50, "USD") // 10050
 * toMinorUnits("123", "JPY") // 123 (JPY has 0 decimal places)
 */
export function toMinorUnits(
  amount: number | string,
  currency: string,
): number {
  // Handle null, undefined, or empty values
  if (amount === null || amount === undefined || amount === "") {
    return 0;
  }

  const meta = getCurrencyMeta(currency);

  // Convert to number if string
  const numericAmount =
    typeof amount === "string" ? parseFloat(amount) : amount;

  // Handle NaN
  if (isNaN(numericAmount)) {
    return 0;
  }

  // Calculate multiplier based on decimal places
  const multiplier = Math.pow(10, meta.decimals);

  // Convert to minor units and round to handle floating point precision issues
  return Math.round(numericAmount * multiplier);
}

/**
 * Convert a stored integer (minor units) to decimal string (major units)
 *
 * @param minorValue - The amount in minor units as an integer
 * @param currency - The currency code (e.g., "USD", "GBP")
 * @returns The amount in major units as a string (e.g., "100.50")
 *
 * @example
 * toMajorUnits(10050, "USD") // "100.50"
 * toMajorUnits(123, "JPY") // "123"
 */
export function toMajorUnits(minorValue: number, currency: string): string {
  // Handle null, undefined, or NaN values
  if (minorValue === null || minorValue === undefined || isNaN(minorValue)) {
    return "0";
  }

  const meta = getCurrencyMeta(currency);

  // Calculate divisor based on decimal places
  const divisor = Math.pow(10, meta.decimals);

  // Convert to major units
  const majorValue = minorValue / divisor;

  // Format with appropriate decimal places
  return majorValue.toFixed(meta.decimals);
}

/**
 * Safely add two amounts in minor units
 *
 * @param amount1 - First amount in minor units
 * @param amount2 - Second amount in minor units
 * @returns Sum in minor units
 */
export function addMinorUnits(amount1: number, amount2: number): number {
  return (amount1 || 0) + (amount2 || 0);
}

/**
 * Safely subtract two amounts in minor units
 *
 * @param amount1 - First amount in minor units
 * @param amount2 - Second amount in minor units
 * @returns Difference in minor units
 */
export function subtractMinorUnits(amount1: number, amount2: number): number {
  return (amount1 || 0) - (amount2 || 0);
}

/**
 * Safely multiply an amount in minor units by a factor
 *
 * @param amount - Amount in minor units
 * @param factor - Multiplication factor
 * @returns Product in minor units (rounded)
 */
export function multiplyMinorUnits(amount: number, factor: number): number {
  return Math.round((amount || 0) * (factor || 0));
}

/**
 * Safely divide an amount in minor units by a divisor
 *
 * @param amount - Amount in minor units
 * @param divisor - Division factor
 * @returns Quotient in minor units (rounded)
 */
export function divideMinorUnits(amount: number, divisor: number): number {
  if (!divisor || divisor === 0) {
    return 0;
  }
  return Math.round((amount || 0) / divisor);
}

/**
 * Calculate percentage of an amount in minor units
 *
 * @param amount - Amount in minor units
 * @param percentage - Percentage (e.g., 15 for 15%)
 * @returns Percentage amount in minor units (rounded)
 */
export function calculatePercentage(
  amount: number,
  percentage: number,
): number {
  return Math.round(((amount || 0) * (percentage || 0)) / 100);
}
